import Navigation from "@/components/Navigation";

export default function About() {
  return (
    <div className="min-h-screen bg-cream">
      <Navigation />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <header className="text-center mb-16 animate-fade-in">
            <h1 className="font-display text-4xl md:text-6xl text-charcoal mb-6">
              About Ink of Sophia
            </h1>
            <p className="text-lg text-brown max-w-2xl mx-auto leading-relaxed">
              A digital sanctuary for the written word, preserving the voices that shaped our understanding of the human condition.
            </p>
          </header>

          <div className="prose prose-lg max-w-none">
            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 mb-12 animate-fade-in">
              <h2 className="font-display text-3xl text-charcoal mb-6">Our Mission</h2>
              <p className="text-charcoal leading-relaxed mb-6">
                <span className="text-6xl float-left font-display text-brown/40 leading-none mr-3 mt-1">I</span>
                nk of Sophia is more than a literary archive—it's a celebration of the transformative power of words. 
                We believe that literature transcends time, offering us windows into different eras, cultures, and 
                perspectives that continue to illuminate our present moment.
              </p>
              <p className="text-charcoal leading-relaxed mb-6">
                Named after Sophia, the ancient Greek concept of wisdom, our archive seeks to preserve both the 
                celebrated voices of literary history and those that have been forgotten by time. From Dostoevsky's 
                psychological depths to the imagined brilliance of fictional authors like Basil Daeren, we honor 
                the full spectrum of literary expression.
              </p>
              <p className="text-charcoal leading-relaxed">
                Each author page is crafted as a gentle introduction to their world—a place where readers can 
                discover biographical insights, explore notable works, and find inspiration through carefully 
                selected quotes that capture the essence of their literary voice.
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 mb-12 animate-fade-in" style={{ animationDelay: '200ms' }}>
              <h2 className="font-display text-3xl text-charcoal mb-6">What You'll Find</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-display text-xl text-brown mb-3">Author Profiles</h3>
                  <p className="text-charcoal leading-relaxed">
                    Intimate biographical sketches that reveal the person behind the pen, exploring the experiences 
                    that shaped their literary voice.
                  </p>
                </div>
                <div>
                  <h3 className="font-display text-xl text-brown mb-3">Notable Works</h3>
                  <p className="text-charcoal leading-relaxed">
                    Curated selections of each author's most significant contributions, with contextual notes to 
                    guide your literary journey.
                  </p>
                </div>
                <div>
                  <h3 className="font-display text-xl text-brown mb-3">Literary Quotes</h3>
                  <p className="text-charcoal leading-relaxed">
                    Carefully chosen passages that distill the essence of each author's wisdom and worldview.
                  </p>
                </div>
                <div>
                  <h3 className="font-display text-xl text-brown mb-3">Forgotten Voices</h3>
                  <p className="text-charcoal leading-relaxed">
                    Special attention to overlooked and underrepresented authors whose words deserve to be 
                    remembered and celebrated.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 animate-fade-in" style={{ animationDelay: '400ms' }}>
              <h2 className="font-display text-3xl text-charcoal mb-6">Our Philosophy</h2>
              <p className="text-charcoal leading-relaxed mb-6">
                We believe that literature is not merely entertainment—it's a form of empathy made manifest. 
                Through the careful study of literary voices, we develop a deeper understanding of the human 
                experience across cultures and centuries.
              </p>
              <p className="text-charcoal leading-relaxed mb-6">
                Our archive is designed as a quiet space for contemplation, free from the noise of modern 
                digital life. Here, you can slow down, reflect, and engage with texts that have moved readers 
                for generations.
              </p>
              <blockquote className="text-center py-8">
                <p className="text-xl text-brown italic">
                  "The real question is not whether machines think but whether men do."
                </p>
                <cite className="text-charcoal text-sm">— B.F. Skinner</cite>
              </blockquote>
              <p className="text-charcoal leading-relaxed">
                In an age of artificial intelligence and rapid technological change, we remain committed to 
                preserving the irreplaceable value of human creativity and the written word.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}