<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> – Ink of Sophia</title>
    <meta name="description" content="A reflective archive of forgotten and famous literary voices. Explore the profound works and thoughts of <PERSON><PERSON><PERSON>." />
    <meta name="author" content="Ink of Sophia" />
    
    <!-- Literary Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400;1,500&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;1,400;1,500&display=swap" rel="stylesheet">

    <!-- <PERSON> and Social Media -->
    <meta property="og:title" content="<PERSON><PERSON><PERSON> – Ink of Sophia" />
    <meta property="og:description" content="A reflective archive of forgotten and famous literary voices. Explore the profound works and thoughts of Fyodor Dostoevsky." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@InkOfSophia" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
