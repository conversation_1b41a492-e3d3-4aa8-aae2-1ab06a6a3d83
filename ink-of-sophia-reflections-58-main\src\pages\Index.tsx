import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";

const Index = () => {
  const featuredAuthors = [
    {
      name: "<PERSON><PERSON><PERSON>",
      slug: "<PERSON><PERSON><PERSON><PERSON>",
      quote: "Pain and suffering are always inevitable for a large intelligence and a deep heart.",
      description: "Russian master of psychological realism"
    },
    {
      name: "<PERSON>",
      slug: "kafka", 
      quote: "A book must be the axe for the frozen sea inside us.",
      description: "Pioneer of existential literature"
    },
    {
      name: "<PERSON>",
      slug: "wilde",
      quote: "We are all in the gutter, but some of us are looking at the stars.",
      description: "Irish wit and aesthetic champion"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      {/* Hero Section */}
      <section className="relative py-24 lg:py-32 bg-gradient-to-br from-background via-muted/30 to-accent/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="font-heading text-hero font-bold text-primary mb-6 animate-fade-in tracking-tight">
            Ink of Sophia
          </h1>
          <p className="font-body text-xl text-muted-foreground mb-12 animate-fade-in-delay max-w-3xl mx-auto leading-relaxed">
            A literary archive preserving the voices of history's greatest writers, 
            from celebrated masters to forgotten poets awaiting rediscovery.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up">
            <Link 
              to="/authors"
              className="inline-flex items-center px-8 py-3 bg-primary text-primary-foreground font-body font-medium rounded-lg shadow-soft hover:shadow-elegant transition-all duration-300 hover:bg-primary/90"
            >
              Explore Authors
            </Link>
            <a 
              href="#featured"
              className="inline-flex items-center px-8 py-3 border border-primary text-primary font-body font-medium rounded-lg hover:bg-primary/10 transition-all duration-300"
            >
              Featured Works
            </a>
          </div>
        </div>
      </section>

      {/* Featured Authors */}
      <section id="featured" className="py-16 lg:py-24">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-heading text-heading font-semibold text-primary mb-12 text-center animate-fade-in">
            Featured Voices
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {featuredAuthors.map((author, index) => (
              <Link
                key={author.slug}
                to={`/author/${author.slug}`}
                className="group animate-scale-in"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-card rounded-lg p-8 shadow-soft hover:shadow-elegant transition-all duration-300 border border-border/50 group-hover:border-primary/20">
                  <h3 className="font-heading text-xl font-semibold text-primary mb-4 group-hover:text-primary/80 transition-colors">
                    {author.name}
                  </h3>
                  <blockquote className="font-literary text-sm italic text-foreground/80 mb-4 leading-relaxed">
                    "{author.quote}"
                  </blockquote>
                  <p className="font-body text-sm text-muted-foreground">
                    {author.description}
                  </p>
                  
                  <div className="mt-6 flex items-center text-primary group-hover:text-primary/80 transition-colors">
                    <span className="font-body text-sm font-medium">Read More</span>
                    <svg className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 lg:py-24 bg-muted/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-heading text-heading font-semibold text-primary mb-8 animate-fade-in">
            Our Mission
          </h2>
          <div className="prose prose-lg max-w-none">
            <p className="font-body text-body leading-relaxed text-foreground/90 mb-6 animate-fade-in-up">
              Literature is the mirror of the human soul, reflecting our deepest fears, 
              greatest hopes, and eternal questions. At Ink of Sophia, we believe that 
              the voices of the past hold timeless wisdom for the present.
            </p>
            <p className="font-body text-body leading-relaxed text-foreground/90 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              From the psychological depths of Dostoevsky to the existential anxieties 
              of Kafka, from the wit of Wilde to the confessional power of Plath, 
              we curate and preserve the literary treasures that continue to shape 
              our understanding of what it means to be human.
            </p>
          </div>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="py-12 bg-muted/50 border-t border-border/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="font-heading text-lg font-semibold text-primary mb-4">
            Ink of Sophia
          </h3>
          <p className="font-body text-sm text-muted-foreground max-w-2xl mx-auto">
            A curated collection of literary voices, preserving the wisdom and beauty 
            of history's greatest writers for future generations.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
