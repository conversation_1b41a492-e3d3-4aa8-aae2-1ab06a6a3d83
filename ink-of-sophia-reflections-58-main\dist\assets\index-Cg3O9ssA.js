var gc=e=>{throw TypeError(e)};var ua=(e,t,n)=>t.has(e)||gc("Cannot "+n);var j=(e,t,n)=>(ua(e,t,"read from private field"),n?n.call(e):t.get(e)),Z=(e,t,n)=>t.has(e)?gc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),U=(e,t,n,r)=>(ua(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Re=(e,t,n)=>(ua(e,t,"access private method"),n);var gi=(e,t,n,r)=>({set _(o){U(e,t,o,n)},get _(){return j(e,t,r)}});function gg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function mf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gf={exports:{}},Os={},yf={exports:{}},G={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ri=Symbol.for("react.element"),yg=Symbol.for("react.portal"),vg=Symbol.for("react.fragment"),xg=Symbol.for("react.strict_mode"),wg=Symbol.for("react.profiler"),bg=Symbol.for("react.provider"),kg=Symbol.for("react.context"),Sg=Symbol.for("react.forward_ref"),Eg=Symbol.for("react.suspense"),Cg=Symbol.for("react.memo"),Pg=Symbol.for("react.lazy"),yc=Symbol.iterator;function jg(e){return e===null||typeof e!="object"?null:(e=yc&&e[yc]||e["@@iterator"],typeof e=="function"?e:null)}var vf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},xf=Object.assign,wf={};function Zr(e,t,n){this.props=e,this.context=t,this.refs=wf,this.updater=n||vf}Zr.prototype.isReactComponent={};Zr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function bf(){}bf.prototype=Zr.prototype;function Xl(e,t,n){this.props=e,this.context=t,this.refs=wf,this.updater=n||vf}var Zl=Xl.prototype=new bf;Zl.constructor=Xl;xf(Zl,Zr.prototype);Zl.isPureReactComponent=!0;var vc=Array.isArray,kf=Object.prototype.hasOwnProperty,eu={current:null},Sf={key:!0,ref:!0,__self:!0,__source:!0};function Ef(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)kf.call(t,r)&&!Sf.hasOwnProperty(r)&&(o[r]=t[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ri,type:e,key:i,ref:s,props:o,_owner:eu.current}}function Ng(e,t){return{$$typeof:ri,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function tu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ri}function Tg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var xc=/\/+/g;function ca(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Tg(""+e.key):t.toString(36)}function Fi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ri:case yg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+ca(s,0):r,vc(o)?(n="",e!=null&&(n=e.replace(xc,"$&/")+"/"),Fi(o,t,n,"",function(c){return c})):o!=null&&(tu(o)&&(o=Ng(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(xc,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",vc(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+ca(i,a);s+=Fi(i,t,n,u,o)}else if(u=jg(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+ca(i,a++),s+=Fi(i,t,n,u,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function yi(e,t,n){if(e==null)return e;var r=[],o=0;return Fi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Ag(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var He={current:null},Bi={transition:null},Rg={ReactCurrentDispatcher:He,ReactCurrentBatchConfig:Bi,ReactCurrentOwner:eu};function Cf(){throw Error("act(...) is not supported in production builds of React.")}G.Children={map:yi,forEach:function(e,t,n){yi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return yi(e,function(){t++}),t},toArray:function(e){return yi(e,function(t){return t})||[]},only:function(e){if(!tu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};G.Component=Zr;G.Fragment=vg;G.Profiler=wg;G.PureComponent=Xl;G.StrictMode=xg;G.Suspense=Eg;G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Rg;G.act=Cf;G.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=xf({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=eu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)kf.call(t,u)&&!Sf.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:ri,type:e.type,key:o,ref:i,props:r,_owner:s}};G.createContext=function(e){return e={$$typeof:kg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:bg,_context:e},e.Consumer=e};G.createElement=Ef;G.createFactory=function(e){var t=Ef.bind(null,e);return t.type=e,t};G.createRef=function(){return{current:null}};G.forwardRef=function(e){return{$$typeof:Sg,render:e}};G.isValidElement=tu;G.lazy=function(e){return{$$typeof:Pg,_payload:{_status:-1,_result:e},_init:Ag}};G.memo=function(e,t){return{$$typeof:Cg,type:e,compare:t===void 0?null:t}};G.startTransition=function(e){var t=Bi.transition;Bi.transition={};try{e()}finally{Bi.transition=t}};G.unstable_act=Cf;G.useCallback=function(e,t){return He.current.useCallback(e,t)};G.useContext=function(e){return He.current.useContext(e)};G.useDebugValue=function(){};G.useDeferredValue=function(e){return He.current.useDeferredValue(e)};G.useEffect=function(e,t){return He.current.useEffect(e,t)};G.useId=function(){return He.current.useId()};G.useImperativeHandle=function(e,t,n){return He.current.useImperativeHandle(e,t,n)};G.useInsertionEffect=function(e,t){return He.current.useInsertionEffect(e,t)};G.useLayoutEffect=function(e,t){return He.current.useLayoutEffect(e,t)};G.useMemo=function(e,t){return He.current.useMemo(e,t)};G.useReducer=function(e,t,n){return He.current.useReducer(e,t,n)};G.useRef=function(e){return He.current.useRef(e)};G.useState=function(e){return He.current.useState(e)};G.useSyncExternalStore=function(e,t,n){return He.current.useSyncExternalStore(e,t,n)};G.useTransition=function(){return He.current.useTransition()};G.version="18.3.1";yf.exports=G;var x=yf.exports;const R=mf(x),Og=gg({__proto__:null,default:R},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lg=x,Mg=Symbol.for("react.element"),_g=Symbol.for("react.fragment"),Ig=Object.prototype.hasOwnProperty,Dg=Lg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,zg={key:!0,ref:!0,__self:!0,__source:!0};function Pf(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Ig.call(t,r)&&!zg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Mg,type:e,key:i,ref:s,props:o,_owner:Dg.current}}Os.Fragment=_g;Os.jsx=Pf;Os.jsxs=Pf;gf.exports=Os;var l=gf.exports,jf={exports:{}},it={},Nf={exports:{}},Tf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,A){var z=E.length;E.push(A);e:for(;0<z;){var _=z-1>>>1,F=E[_];if(0<o(F,A))E[_]=A,E[z]=F,z=_;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var A=E[0],z=E.pop();if(z!==A){E[0]=z;e:for(var _=0,F=E.length,Y=F>>>1;_<Y;){var he=2*(_+1)-1,Ye=E[he],oe=he+1,pt=E[oe];if(0>o(Ye,z))oe<F&&0>o(pt,Ye)?(E[_]=pt,E[oe]=z,_=oe):(E[_]=Ye,E[he]=z,_=he);else if(oe<F&&0>o(pt,z))E[_]=pt,E[oe]=z,_=oe;else break e}}return A}function o(E,A){var z=E.sortIndex-A.sortIndex;return z!==0?z:E.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var u=[],c=[],f=1,h=null,d=3,v=!1,w=!1,y=!1,b=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(E){for(var A=n(c);A!==null;){if(A.callback===null)r(c);else if(A.startTime<=E)r(c),A.sortIndex=A.expirationTime,t(u,A);else break;A=n(c)}}function k(E){if(y=!1,g(E),!w)if(n(u)!==null)w=!0,$(S);else{var A=n(c);A!==null&&Q(k,A.startTime-E)}}function S(E,A){w=!1,y&&(y=!1,m(N),N=-1),v=!0;var z=d;try{for(g(A),h=n(u);h!==null&&(!(h.expirationTime>A)||E&&!B());){var _=h.callback;if(typeof _=="function"){h.callback=null,d=h.priorityLevel;var F=_(h.expirationTime<=A);A=e.unstable_now(),typeof F=="function"?h.callback=F:h===n(u)&&r(u),g(A)}else r(u);h=n(u)}if(h!==null)var Y=!0;else{var he=n(c);he!==null&&Q(k,he.startTime-A),Y=!1}return Y}finally{h=null,d=z,v=!1}}var C=!1,P=null,N=-1,I=5,O=-1;function B(){return!(e.unstable_now()-O<I)}function D(){if(P!==null){var E=e.unstable_now();O=E;var A=!0;try{A=P(!0,E)}finally{A?V():(C=!1,P=null)}}else C=!1}var V;if(typeof p=="function")V=function(){p(D)};else if(typeof MessageChannel<"u"){var L=new MessageChannel,q=L.port2;L.port1.onmessage=D,V=function(){q.postMessage(null)}}else V=function(){b(D,0)};function $(E){P=E,C||(C=!0,V())}function Q(E,A){N=b(function(){E(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){w||v||(w=!0,$(S))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(E){switch(d){case 1:case 2:case 3:var A=3;break;default:A=d}var z=d;d=A;try{return E()}finally{d=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,A){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var z=d;d=E;try{return A()}finally{d=z}},e.unstable_scheduleCallback=function(E,A,z){var _=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?_+z:_):z=_,E){case 1:var F=-1;break;case 2:F=250;break;case 5:F=**********;break;case 4:F=1e4;break;default:F=5e3}return F=z+F,E={id:f++,callback:A,priorityLevel:E,startTime:z,expirationTime:F,sortIndex:-1},z>_?(E.sortIndex=z,t(c,E),n(u)===null&&E===n(c)&&(y?(m(N),N=-1):y=!0,Q(k,z-_))):(E.sortIndex=F,t(u,E),w||v||(w=!0,$(S))),E},e.unstable_shouldYield=B,e.unstable_wrapCallback=function(E){var A=d;return function(){var z=d;d=A;try{return E.apply(this,arguments)}finally{d=z}}}})(Tf);Nf.exports=Tf;var Fg=Nf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bg=x,ot=Fg;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Af=new Set,Oo={};function ir(e,t){Ur(e,t),Ur(e+"Capture",t)}function Ur(e,t){for(Oo[e]=t,e=0;e<t.length;e++)Af.add(t[e])}var Qt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ua=Object.prototype.hasOwnProperty,$g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,wc={},bc={};function Hg(e){return Ua.call(bc,e)?!0:Ua.call(wc,e)?!1:$g.test(e)?bc[e]=!0:(wc[e]=!0,!1)}function Wg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ug(e,t,n,r){if(t===null||typeof t>"u"||Wg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function We(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ae={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ae[e]=new We(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ae[t]=new We(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ae[e]=new We(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ae[e]=new We(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ae[e]=new We(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ae[e]=new We(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ae[e]=new We(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ae[e]=new We(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ae[e]=new We(e,5,!1,e.toLowerCase(),null,!1,!1)});var nu=/[\-:]([a-z])/g;function ru(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(nu,ru);Ae[t]=new We(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(nu,ru);Ae[t]=new We(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(nu,ru);Ae[t]=new We(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ae[e]=new We(e,1,!1,e.toLowerCase(),null,!1,!1)});Ae.xlinkHref=new We("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ae[e]=new We(e,1,!1,e.toLowerCase(),null,!0,!0)});function ou(e,t,n,r){var o=Ae.hasOwnProperty(t)?Ae[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ug(t,n,o,r)&&(n=null),r||o===null?Hg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Zt=Bg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vi=Symbol.for("react.element"),mr=Symbol.for("react.portal"),gr=Symbol.for("react.fragment"),iu=Symbol.for("react.strict_mode"),Va=Symbol.for("react.profiler"),Rf=Symbol.for("react.provider"),Of=Symbol.for("react.context"),su=Symbol.for("react.forward_ref"),qa=Symbol.for("react.suspense"),Qa=Symbol.for("react.suspense_list"),au=Symbol.for("react.memo"),cn=Symbol.for("react.lazy"),Lf=Symbol.for("react.offscreen"),kc=Symbol.iterator;function ao(e){return e===null||typeof e!="object"?null:(e=kc&&e[kc]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Object.assign,da;function vo(e){if(da===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);da=t&&t[1]||""}return`
`+da+e}var fa=!1;function ha(e,t){if(!e||fa)return"";fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var u=`
`+o[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=a);break}}}finally{fa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vo(e):""}function Vg(e){switch(e.tag){case 5:return vo(e.type);case 16:return vo("Lazy");case 13:return vo("Suspense");case 19:return vo("SuspenseList");case 0:case 2:case 15:return e=ha(e.type,!1),e;case 11:return e=ha(e.type.render,!1),e;case 1:return e=ha(e.type,!0),e;default:return""}}function Ka(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case gr:return"Fragment";case mr:return"Portal";case Va:return"Profiler";case iu:return"StrictMode";case qa:return"Suspense";case Qa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Of:return(e.displayName||"Context")+".Consumer";case Rf:return(e._context.displayName||"Context")+".Provider";case su:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case au:return t=e.displayName||null,t!==null?t:Ka(e.type)||"Memo";case cn:t=e._payload,e=e._init;try{return Ka(e(t))}catch{}}return null}function qg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ka(t);case 8:return t===iu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Rn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Mf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Qg(e){var t=Mf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xi(e){e._valueTracker||(e._valueTracker=Qg(e))}function _f(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Mf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function es(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ga(e,t){var n=t.checked;return ge({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Sc(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Rn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function If(e,t){t=t.checked,t!=null&&ou(e,"checked",t,!1)}function Ya(e,t){If(e,t);var n=Rn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ja(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ja(e,t.type,Rn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ec(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ja(e,t,n){(t!=="number"||es(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var xo=Array.isArray;function jr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Rn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Xa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return ge({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Cc(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(xo(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Rn(n)}}function Df(e,t){var n=Rn(t.value),r=Rn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Pc(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function zf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Za(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?zf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var wi,Ff=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(wi=wi||document.createElement("div"),wi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=wi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Lo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ko={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Kg=["Webkit","ms","Moz","O"];Object.keys(ko).forEach(function(e){Kg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ko[t]=ko[e]})});function Bf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ko.hasOwnProperty(e)&&ko[e]?(""+t).trim():t+"px"}function $f(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Bf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Gg=ge({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function el(e,t){if(t){if(Gg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function tl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var nl=null;function lu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var rl=null,Nr=null,Tr=null;function jc(e){if(e=si(e)){if(typeof rl!="function")throw Error(T(280));var t=e.stateNode;t&&(t=Ds(t),rl(e.stateNode,e.type,t))}}function Hf(e){Nr?Tr?Tr.push(e):Tr=[e]:Nr=e}function Wf(){if(Nr){var e=Nr,t=Tr;if(Tr=Nr=null,jc(e),t)for(e=0;e<t.length;e++)jc(t[e])}}function Uf(e,t){return e(t)}function Vf(){}var pa=!1;function qf(e,t,n){if(pa)return e(t,n);pa=!0;try{return Uf(e,t,n)}finally{pa=!1,(Nr!==null||Tr!==null)&&(Vf(),Wf())}}function Mo(e,t){var n=e.stateNode;if(n===null)return null;var r=Ds(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var ol=!1;if(Qt)try{var lo={};Object.defineProperty(lo,"passive",{get:function(){ol=!0}}),window.addEventListener("test",lo,lo),window.removeEventListener("test",lo,lo)}catch{ol=!1}function Yg(e,t,n,r,o,i,s,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var So=!1,ts=null,ns=!1,il=null,Jg={onError:function(e){So=!0,ts=e}};function Xg(e,t,n,r,o,i,s,a,u){So=!1,ts=null,Yg.apply(Jg,arguments)}function Zg(e,t,n,r,o,i,s,a,u){if(Xg.apply(this,arguments),So){if(So){var c=ts;So=!1,ts=null}else throw Error(T(198));ns||(ns=!0,il=c)}}function sr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Qf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Nc(e){if(sr(e)!==e)throw Error(T(188))}function ey(e){var t=e.alternate;if(!t){if(t=sr(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Nc(o),e;if(i===r)return Nc(o),t;i=i.sibling}throw Error(T(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Kf(e){return e=ey(e),e!==null?Gf(e):null}function Gf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Gf(e);if(t!==null)return t;e=e.sibling}return null}var Yf=ot.unstable_scheduleCallback,Tc=ot.unstable_cancelCallback,ty=ot.unstable_shouldYield,ny=ot.unstable_requestPaint,xe=ot.unstable_now,ry=ot.unstable_getCurrentPriorityLevel,uu=ot.unstable_ImmediatePriority,Jf=ot.unstable_UserBlockingPriority,rs=ot.unstable_NormalPriority,oy=ot.unstable_LowPriority,Xf=ot.unstable_IdlePriority,Ls=null,_t=null;function iy(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(Ls,e,void 0,(e.current.flags&128)===128)}catch{}}var bt=Math.clz32?Math.clz32:ly,sy=Math.log,ay=Math.LN2;function ly(e){return e>>>=0,e===0?32:31-(sy(e)/ay|0)|0}var bi=64,ki=4194304;function wo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function os(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=wo(a):(i&=s,i!==0&&(r=wo(i)))}else s=n&~o,s!==0?r=wo(s):i!==0&&(r=wo(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-bt(t),o=1<<n,r|=e[n],t&=~o;return r}function uy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function cy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-bt(i),a=1<<s,u=o[s];u===-1?(!(a&n)||a&r)&&(o[s]=uy(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function sl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Zf(){var e=bi;return bi<<=1,!(bi&4194240)&&(bi=64),e}function ma(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function oi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-bt(t),e[t]=n}function dy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-bt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function cu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-bt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ie=0;function eh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var th,du,nh,rh,oh,al=!1,Si=[],kn=null,Sn=null,En=null,_o=new Map,Io=new Map,fn=[],fy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ac(e,t){switch(e){case"focusin":case"focusout":kn=null;break;case"dragenter":case"dragleave":Sn=null;break;case"mouseover":case"mouseout":En=null;break;case"pointerover":case"pointerout":_o.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Io.delete(t.pointerId)}}function uo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=si(t),t!==null&&du(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function hy(e,t,n,r,o){switch(t){case"focusin":return kn=uo(kn,e,t,n,r,o),!0;case"dragenter":return Sn=uo(Sn,e,t,n,r,o),!0;case"mouseover":return En=uo(En,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return _o.set(i,uo(_o.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Io.set(i,uo(Io.get(i)||null,e,t,n,r,o)),!0}return!1}function ih(e){var t=Wn(e.target);if(t!==null){var n=sr(t);if(n!==null){if(t=n.tag,t===13){if(t=Qf(n),t!==null){e.blockedOn=t,oh(e.priority,function(){nh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function $i(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ll(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);nl=r,n.target.dispatchEvent(r),nl=null}else return t=si(n),t!==null&&du(t),e.blockedOn=n,!1;t.shift()}return!0}function Rc(e,t,n){$i(e)&&n.delete(t)}function py(){al=!1,kn!==null&&$i(kn)&&(kn=null),Sn!==null&&$i(Sn)&&(Sn=null),En!==null&&$i(En)&&(En=null),_o.forEach(Rc),Io.forEach(Rc)}function co(e,t){e.blockedOn===t&&(e.blockedOn=null,al||(al=!0,ot.unstable_scheduleCallback(ot.unstable_NormalPriority,py)))}function Do(e){function t(o){return co(o,e)}if(0<Si.length){co(Si[0],e);for(var n=1;n<Si.length;n++){var r=Si[n];r.blockedOn===e&&(r.blockedOn=null)}}for(kn!==null&&co(kn,e),Sn!==null&&co(Sn,e),En!==null&&co(En,e),_o.forEach(t),Io.forEach(t),n=0;n<fn.length;n++)r=fn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<fn.length&&(n=fn[0],n.blockedOn===null);)ih(n),n.blockedOn===null&&fn.shift()}var Ar=Zt.ReactCurrentBatchConfig,is=!0;function my(e,t,n,r){var o=ie,i=Ar.transition;Ar.transition=null;try{ie=1,fu(e,t,n,r)}finally{ie=o,Ar.transition=i}}function gy(e,t,n,r){var o=ie,i=Ar.transition;Ar.transition=null;try{ie=4,fu(e,t,n,r)}finally{ie=o,Ar.transition=i}}function fu(e,t,n,r){if(is){var o=ll(e,t,n,r);if(o===null)Ca(e,t,r,ss,n),Ac(e,r);else if(hy(o,e,t,n,r))r.stopPropagation();else if(Ac(e,r),t&4&&-1<fy.indexOf(e)){for(;o!==null;){var i=si(o);if(i!==null&&th(i),i=ll(e,t,n,r),i===null&&Ca(e,t,r,ss,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ca(e,t,r,null,n)}}var ss=null;function ll(e,t,n,r){if(ss=null,e=lu(r),e=Wn(e),e!==null)if(t=sr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Qf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ss=e,null}function sh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ry()){case uu:return 1;case Jf:return 4;case rs:case oy:return 16;case Xf:return 536870912;default:return 16}default:return 16}}var xn=null,hu=null,Hi=null;function ah(){if(Hi)return Hi;var e,t=hu,n=t.length,r,o="value"in xn?xn.value:xn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Hi=o.slice(e,1<r?1-r:void 0)}function Wi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ei(){return!0}function Oc(){return!1}function st(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ei:Oc,this.isPropagationStopped=Oc,this}return ge(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ei)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ei)},persist:function(){},isPersistent:Ei}),t}var eo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pu=st(eo),ii=ge({},eo,{view:0,detail:0}),yy=st(ii),ga,ya,fo,Ms=ge({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fo&&(fo&&e.type==="mousemove"?(ga=e.screenX-fo.screenX,ya=e.screenY-fo.screenY):ya=ga=0,fo=e),ga)},movementY:function(e){return"movementY"in e?e.movementY:ya}}),Lc=st(Ms),vy=ge({},Ms,{dataTransfer:0}),xy=st(vy),wy=ge({},ii,{relatedTarget:0}),va=st(wy),by=ge({},eo,{animationName:0,elapsedTime:0,pseudoElement:0}),ky=st(by),Sy=ge({},eo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ey=st(Sy),Cy=ge({},eo,{data:0}),Mc=st(Cy),Py={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ny={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ty(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ny[e])?!!t[e]:!1}function mu(){return Ty}var Ay=ge({},ii,{key:function(e){if(e.key){var t=Py[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Wi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mu,charCode:function(e){return e.type==="keypress"?Wi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Wi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ry=st(Ay),Oy=ge({},Ms,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),_c=st(Oy),Ly=ge({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mu}),My=st(Ly),_y=ge({},eo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Iy=st(_y),Dy=ge({},Ms,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zy=st(Dy),Fy=[9,13,27,32],gu=Qt&&"CompositionEvent"in window,Eo=null;Qt&&"documentMode"in document&&(Eo=document.documentMode);var By=Qt&&"TextEvent"in window&&!Eo,lh=Qt&&(!gu||Eo&&8<Eo&&11>=Eo),Ic=" ",Dc=!1;function uh(e,t){switch(e){case"keyup":return Fy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ch(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yr=!1;function $y(e,t){switch(e){case"compositionend":return ch(t);case"keypress":return t.which!==32?null:(Dc=!0,Ic);case"textInput":return e=t.data,e===Ic&&Dc?null:e;default:return null}}function Hy(e,t){if(yr)return e==="compositionend"||!gu&&uh(e,t)?(e=ah(),Hi=hu=xn=null,yr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return lh&&t.locale!=="ko"?null:t.data;default:return null}}var Wy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Wy[e.type]:t==="textarea"}function dh(e,t,n,r){Hf(r),t=as(t,"onChange"),0<t.length&&(n=new pu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Co=null,zo=null;function Uy(e){kh(e,0)}function _s(e){var t=wr(e);if(_f(t))return e}function Vy(e,t){if(e==="change")return t}var fh=!1;if(Qt){var xa;if(Qt){var wa="oninput"in document;if(!wa){var Fc=document.createElement("div");Fc.setAttribute("oninput","return;"),wa=typeof Fc.oninput=="function"}xa=wa}else xa=!1;fh=xa&&(!document.documentMode||9<document.documentMode)}function Bc(){Co&&(Co.detachEvent("onpropertychange",hh),zo=Co=null)}function hh(e){if(e.propertyName==="value"&&_s(zo)){var t=[];dh(t,zo,e,lu(e)),qf(Uy,t)}}function qy(e,t,n){e==="focusin"?(Bc(),Co=t,zo=n,Co.attachEvent("onpropertychange",hh)):e==="focusout"&&Bc()}function Qy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return _s(zo)}function Ky(e,t){if(e==="click")return _s(t)}function Gy(e,t){if(e==="input"||e==="change")return _s(t)}function Yy(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var St=typeof Object.is=="function"?Object.is:Yy;function Fo(e,t){if(St(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Ua.call(t,o)||!St(e[o],t[o]))return!1}return!0}function $c(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Hc(e,t){var n=$c(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=$c(n)}}function ph(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ph(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function mh(){for(var e=window,t=es();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=es(e.document)}return t}function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Jy(e){var t=mh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ph(n.ownerDocument.documentElement,n)){if(r!==null&&yu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Hc(n,i);var s=Hc(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Xy=Qt&&"documentMode"in document&&11>=document.documentMode,vr=null,ul=null,Po=null,cl=!1;function Wc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;cl||vr==null||vr!==es(r)||(r=vr,"selectionStart"in r&&yu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Po&&Fo(Po,r)||(Po=r,r=as(ul,"onSelect"),0<r.length&&(t=new pu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function Ci(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var xr={animationend:Ci("Animation","AnimationEnd"),animationiteration:Ci("Animation","AnimationIteration"),animationstart:Ci("Animation","AnimationStart"),transitionend:Ci("Transition","TransitionEnd")},ba={},gh={};Qt&&(gh=document.createElement("div").style,"AnimationEvent"in window||(delete xr.animationend.animation,delete xr.animationiteration.animation,delete xr.animationstart.animation),"TransitionEvent"in window||delete xr.transitionend.transition);function Is(e){if(ba[e])return ba[e];if(!xr[e])return e;var t=xr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in gh)return ba[e]=t[n];return e}var yh=Is("animationend"),vh=Is("animationiteration"),xh=Is("animationstart"),wh=Is("transitionend"),bh=new Map,Uc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Dn(e,t){bh.set(e,t),ir(t,[e])}for(var ka=0;ka<Uc.length;ka++){var Sa=Uc[ka],Zy=Sa.toLowerCase(),ev=Sa[0].toUpperCase()+Sa.slice(1);Dn(Zy,"on"+ev)}Dn(yh,"onAnimationEnd");Dn(vh,"onAnimationIteration");Dn(xh,"onAnimationStart");Dn("dblclick","onDoubleClick");Dn("focusin","onFocus");Dn("focusout","onBlur");Dn(wh,"onTransitionEnd");Ur("onMouseEnter",["mouseout","mouseover"]);Ur("onMouseLeave",["mouseout","mouseover"]);Ur("onPointerEnter",["pointerout","pointerover"]);Ur("onPointerLeave",["pointerout","pointerover"]);ir("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ir("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ir("onBeforeInput",["compositionend","keypress","textInput","paste"]);ir("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ir("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var bo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),tv=new Set("cancel close invalid load scroll toggle".split(" ").concat(bo));function Vc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Zg(r,t,void 0,e),e.currentTarget=null}function kh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&o.isPropagationStopped())break e;Vc(o,a,c),i=u}else for(s=0;s<r.length;s++){if(a=r[s],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&o.isPropagationStopped())break e;Vc(o,a,c),i=u}}}if(ns)throw e=il,ns=!1,il=null,e}function ce(e,t){var n=t[ml];n===void 0&&(n=t[ml]=new Set);var r=e+"__bubble";n.has(r)||(Sh(t,e,2,!1),n.add(r))}function Ea(e,t,n){var r=0;t&&(r|=4),Sh(n,e,r,t)}var Pi="_reactListening"+Math.random().toString(36).slice(2);function Bo(e){if(!e[Pi]){e[Pi]=!0,Af.forEach(function(n){n!=="selectionchange"&&(tv.has(n)||Ea(n,!1,e),Ea(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Pi]||(t[Pi]=!0,Ea("selectionchange",!1,t))}}function Sh(e,t,n,r){switch(sh(t)){case 1:var o=my;break;case 4:o=gy;break;default:o=fu}n=o.bind(null,t,n,e),o=void 0,!ol||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ca(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===o||u.nodeType===8&&u.parentNode===o))return;s=s.return}for(;a!==null;){if(s=Wn(a),s===null)return;if(u=s.tag,u===5||u===6){r=i=s;continue e}a=a.parentNode}}r=r.return}qf(function(){var c=i,f=lu(n),h=[];e:{var d=bh.get(e);if(d!==void 0){var v=pu,w=e;switch(e){case"keypress":if(Wi(n)===0)break e;case"keydown":case"keyup":v=Ry;break;case"focusin":w="focus",v=va;break;case"focusout":w="blur",v=va;break;case"beforeblur":case"afterblur":v=va;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Lc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=xy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=My;break;case yh:case vh:case xh:v=ky;break;case wh:v=Iy;break;case"scroll":v=yy;break;case"wheel":v=zy;break;case"copy":case"cut":case"paste":v=Ey;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=_c}var y=(t&4)!==0,b=!y&&e==="scroll",m=y?d!==null?d+"Capture":null:d;y=[];for(var p=c,g;p!==null;){g=p;var k=g.stateNode;if(g.tag===5&&k!==null&&(g=k,m!==null&&(k=Mo(p,m),k!=null&&y.push($o(p,k,g)))),b)break;p=p.return}0<y.length&&(d=new v(d,w,null,n,f),h.push({event:d,listeners:y}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",d&&n!==nl&&(w=n.relatedTarget||n.fromElement)&&(Wn(w)||w[Kt]))break e;if((v||d)&&(d=f.window===f?f:(d=f.ownerDocument)?d.defaultView||d.parentWindow:window,v?(w=n.relatedTarget||n.toElement,v=c,w=w?Wn(w):null,w!==null&&(b=sr(w),w!==b||w.tag!==5&&w.tag!==6)&&(w=null)):(v=null,w=c),v!==w)){if(y=Lc,k="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(y=_c,k="onPointerLeave",m="onPointerEnter",p="pointer"),b=v==null?d:wr(v),g=w==null?d:wr(w),d=new y(k,p+"leave",v,n,f),d.target=b,d.relatedTarget=g,k=null,Wn(f)===c&&(y=new y(m,p+"enter",w,n,f),y.target=g,y.relatedTarget=b,k=y),b=k,v&&w)t:{for(y=v,m=w,p=0,g=y;g;g=pr(g))p++;for(g=0,k=m;k;k=pr(k))g++;for(;0<p-g;)y=pr(y),p--;for(;0<g-p;)m=pr(m),g--;for(;p--;){if(y===m||m!==null&&y===m.alternate)break t;y=pr(y),m=pr(m)}y=null}else y=null;v!==null&&qc(h,d,v,y,!1),w!==null&&b!==null&&qc(h,b,w,y,!0)}}e:{if(d=c?wr(c):window,v=d.nodeName&&d.nodeName.toLowerCase(),v==="select"||v==="input"&&d.type==="file")var S=Vy;else if(zc(d))if(fh)S=Gy;else{S=Qy;var C=qy}else(v=d.nodeName)&&v.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=Ky);if(S&&(S=S(e,c))){dh(h,S,n,f);break e}C&&C(e,d,c),e==="focusout"&&(C=d._wrapperState)&&C.controlled&&d.type==="number"&&Ja(d,"number",d.value)}switch(C=c?wr(c):window,e){case"focusin":(zc(C)||C.contentEditable==="true")&&(vr=C,ul=c,Po=null);break;case"focusout":Po=ul=vr=null;break;case"mousedown":cl=!0;break;case"contextmenu":case"mouseup":case"dragend":cl=!1,Wc(h,n,f);break;case"selectionchange":if(Xy)break;case"keydown":case"keyup":Wc(h,n,f)}var P;if(gu)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else yr?uh(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(lh&&n.locale!=="ko"&&(yr||N!=="onCompositionStart"?N==="onCompositionEnd"&&yr&&(P=ah()):(xn=f,hu="value"in xn?xn.value:xn.textContent,yr=!0)),C=as(c,N),0<C.length&&(N=new Mc(N,e,null,n,f),h.push({event:N,listeners:C}),P?N.data=P:(P=ch(n),P!==null&&(N.data=P)))),(P=By?$y(e,n):Hy(e,n))&&(c=as(c,"onBeforeInput"),0<c.length&&(f=new Mc("onBeforeInput","beforeinput",null,n,f),h.push({event:f,listeners:c}),f.data=P))}kh(h,t)})}function $o(e,t,n){return{instance:e,listener:t,currentTarget:n}}function as(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Mo(e,n),i!=null&&r.unshift($o(e,i,o)),i=Mo(e,t),i!=null&&r.push($o(e,i,o))),e=e.return}return r}function pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function qc(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,o?(u=Mo(n,i),u!=null&&s.unshift($o(n,u,a))):o||(u=Mo(n,i),u!=null&&s.push($o(n,u,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var nv=/\r\n?/g,rv=/\u0000|\uFFFD/g;function Qc(e){return(typeof e=="string"?e:""+e).replace(nv,`
`).replace(rv,"")}function ji(e,t,n){if(t=Qc(t),Qc(e)!==t&&n)throw Error(T(425))}function ls(){}var dl=null,fl=null;function hl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var pl=typeof setTimeout=="function"?setTimeout:void 0,ov=typeof clearTimeout=="function"?clearTimeout:void 0,Kc=typeof Promise=="function"?Promise:void 0,iv=typeof queueMicrotask=="function"?queueMicrotask:typeof Kc<"u"?function(e){return Kc.resolve(null).then(e).catch(sv)}:pl;function sv(e){setTimeout(function(){throw e})}function Pa(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Do(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Do(t)}function Cn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Gc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var to=Math.random().toString(36).slice(2),Mt="__reactFiber$"+to,Ho="__reactProps$"+to,Kt="__reactContainer$"+to,ml="__reactEvents$"+to,av="__reactListeners$"+to,lv="__reactHandles$"+to;function Wn(e){var t=e[Mt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Kt]||n[Mt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Gc(e);e!==null;){if(n=e[Mt])return n;e=Gc(e)}return t}e=n,n=e.parentNode}return null}function si(e){return e=e[Mt]||e[Kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function wr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function Ds(e){return e[Ho]||null}var gl=[],br=-1;function zn(e){return{current:e}}function de(e){0>br||(e.current=gl[br],gl[br]=null,br--)}function ae(e,t){br++,gl[br]=e.current,e.current=t}var On={},Ie=zn(On),qe=zn(!1),Xn=On;function Vr(e,t){var n=e.type.contextTypes;if(!n)return On;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Qe(e){return e=e.childContextTypes,e!=null}function us(){de(qe),de(Ie)}function Yc(e,t,n){if(Ie.current!==On)throw Error(T(168));ae(Ie,t),ae(qe,n)}function Eh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(T(108,qg(e)||"Unknown",o));return ge({},n,r)}function cs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||On,Xn=Ie.current,ae(Ie,e),ae(qe,qe.current),!0}function Jc(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=Eh(e,t,Xn),r.__reactInternalMemoizedMergedChildContext=e,de(qe),de(Ie),ae(Ie,e)):de(qe),ae(qe,n)}var Wt=null,zs=!1,ja=!1;function Ch(e){Wt===null?Wt=[e]:Wt.push(e)}function uv(e){zs=!0,Ch(e)}function Fn(){if(!ja&&Wt!==null){ja=!0;var e=0,t=ie;try{var n=Wt;for(ie=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Wt=null,zs=!1}catch(o){throw Wt!==null&&(Wt=Wt.slice(e+1)),Yf(uu,Fn),o}finally{ie=t,ja=!1}}return null}var kr=[],Sr=0,ds=null,fs=0,lt=[],ut=0,Zn=null,Ut=1,Vt="";function $n(e,t){kr[Sr++]=fs,kr[Sr++]=ds,ds=e,fs=t}function Ph(e,t,n){lt[ut++]=Ut,lt[ut++]=Vt,lt[ut++]=Zn,Zn=e;var r=Ut;e=Vt;var o=32-bt(r)-1;r&=~(1<<o),n+=1;var i=32-bt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Ut=1<<32-bt(t)+o|n<<o|r,Vt=i+e}else Ut=1<<i|n<<o|r,Vt=e}function vu(e){e.return!==null&&($n(e,1),Ph(e,1,0))}function xu(e){for(;e===ds;)ds=kr[--Sr],kr[Sr]=null,fs=kr[--Sr],kr[Sr]=null;for(;e===Zn;)Zn=lt[--ut],lt[ut]=null,Vt=lt[--ut],lt[ut]=null,Ut=lt[--ut],lt[ut]=null}var nt=null,tt=null,fe=!1,wt=null;function jh(e,t){var n=ct(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Xc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,tt=Cn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,tt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Zn!==null?{id:Ut,overflow:Vt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ct(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,nt=e,tt=null,!0):!1;default:return!1}}function yl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function vl(e){if(fe){var t=tt;if(t){var n=t;if(!Xc(e,t)){if(yl(e))throw Error(T(418));t=Cn(n.nextSibling);var r=nt;t&&Xc(e,t)?jh(r,n):(e.flags=e.flags&-4097|2,fe=!1,nt=e)}}else{if(yl(e))throw Error(T(418));e.flags=e.flags&-4097|2,fe=!1,nt=e}}}function Zc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function Ni(e){if(e!==nt)return!1;if(!fe)return Zc(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!hl(e.type,e.memoizedProps)),t&&(t=tt)){if(yl(e))throw Nh(),Error(T(418));for(;t;)jh(e,t),t=Cn(t.nextSibling)}if(Zc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){tt=Cn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}tt=null}}else tt=nt?Cn(e.stateNode.nextSibling):null;return!0}function Nh(){for(var e=tt;e;)e=Cn(e.nextSibling)}function qr(){tt=nt=null,fe=!1}function wu(e){wt===null?wt=[e]:wt.push(e)}var cv=Zt.ReactCurrentBatchConfig;function ho(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=o.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Ti(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ed(e){var t=e._init;return t(e._payload)}function Th(e){function t(m,p){if(e){var g=m.deletions;g===null?(m.deletions=[p],m.flags|=16):g.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function o(m,p){return m=Tn(m,p),m.index=0,m.sibling=null,m}function i(m,p,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<p?(m.flags|=2,p):g):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,p,g,k){return p===null||p.tag!==6?(p=Ma(g,m.mode,k),p.return=m,p):(p=o(p,g),p.return=m,p)}function u(m,p,g,k){var S=g.type;return S===gr?f(m,p,g.props.children,k,g.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===cn&&ed(S)===p.type)?(k=o(p,g.props),k.ref=ho(m,p,g),k.return=m,k):(k=Yi(g.type,g.key,g.props,null,m.mode,k),k.ref=ho(m,p,g),k.return=m,k)}function c(m,p,g,k){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=_a(g,m.mode,k),p.return=m,p):(p=o(p,g.children||[]),p.return=m,p)}function f(m,p,g,k,S){return p===null||p.tag!==7?(p=Jn(g,m.mode,k,S),p.return=m,p):(p=o(p,g),p.return=m,p)}function h(m,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Ma(""+p,m.mode,g),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case vi:return g=Yi(p.type,p.key,p.props,null,m.mode,g),g.ref=ho(m,null,p),g.return=m,g;case mr:return p=_a(p,m.mode,g),p.return=m,p;case cn:var k=p._init;return h(m,k(p._payload),g)}if(xo(p)||ao(p))return p=Jn(p,m.mode,g,null),p.return=m,p;Ti(m,p)}return null}function d(m,p,g,k){var S=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return S!==null?null:a(m,p,""+g,k);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case vi:return g.key===S?u(m,p,g,k):null;case mr:return g.key===S?c(m,p,g,k):null;case cn:return S=g._init,d(m,p,S(g._payload),k)}if(xo(g)||ao(g))return S!==null?null:f(m,p,g,k,null);Ti(m,g)}return null}function v(m,p,g,k,S){if(typeof k=="string"&&k!==""||typeof k=="number")return m=m.get(g)||null,a(p,m,""+k,S);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case vi:return m=m.get(k.key===null?g:k.key)||null,u(p,m,k,S);case mr:return m=m.get(k.key===null?g:k.key)||null,c(p,m,k,S);case cn:var C=k._init;return v(m,p,g,C(k._payload),S)}if(xo(k)||ao(k))return m=m.get(g)||null,f(p,m,k,S,null);Ti(p,k)}return null}function w(m,p,g,k){for(var S=null,C=null,P=p,N=p=0,I=null;P!==null&&N<g.length;N++){P.index>N?(I=P,P=null):I=P.sibling;var O=d(m,P,g[N],k);if(O===null){P===null&&(P=I);break}e&&P&&O.alternate===null&&t(m,P),p=i(O,p,N),C===null?S=O:C.sibling=O,C=O,P=I}if(N===g.length)return n(m,P),fe&&$n(m,N),S;if(P===null){for(;N<g.length;N++)P=h(m,g[N],k),P!==null&&(p=i(P,p,N),C===null?S=P:C.sibling=P,C=P);return fe&&$n(m,N),S}for(P=r(m,P);N<g.length;N++)I=v(P,m,N,g[N],k),I!==null&&(e&&I.alternate!==null&&P.delete(I.key===null?N:I.key),p=i(I,p,N),C===null?S=I:C.sibling=I,C=I);return e&&P.forEach(function(B){return t(m,B)}),fe&&$n(m,N),S}function y(m,p,g,k){var S=ao(g);if(typeof S!="function")throw Error(T(150));if(g=S.call(g),g==null)throw Error(T(151));for(var C=S=null,P=p,N=p=0,I=null,O=g.next();P!==null&&!O.done;N++,O=g.next()){P.index>N?(I=P,P=null):I=P.sibling;var B=d(m,P,O.value,k);if(B===null){P===null&&(P=I);break}e&&P&&B.alternate===null&&t(m,P),p=i(B,p,N),C===null?S=B:C.sibling=B,C=B,P=I}if(O.done)return n(m,P),fe&&$n(m,N),S;if(P===null){for(;!O.done;N++,O=g.next())O=h(m,O.value,k),O!==null&&(p=i(O,p,N),C===null?S=O:C.sibling=O,C=O);return fe&&$n(m,N),S}for(P=r(m,P);!O.done;N++,O=g.next())O=v(P,m,N,O.value,k),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?N:O.key),p=i(O,p,N),C===null?S=O:C.sibling=O,C=O);return e&&P.forEach(function(D){return t(m,D)}),fe&&$n(m,N),S}function b(m,p,g,k){if(typeof g=="object"&&g!==null&&g.type===gr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case vi:e:{for(var S=g.key,C=p;C!==null;){if(C.key===S){if(S=g.type,S===gr){if(C.tag===7){n(m,C.sibling),p=o(C,g.props.children),p.return=m,m=p;break e}}else if(C.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===cn&&ed(S)===C.type){n(m,C.sibling),p=o(C,g.props),p.ref=ho(m,C,g),p.return=m,m=p;break e}n(m,C);break}else t(m,C);C=C.sibling}g.type===gr?(p=Jn(g.props.children,m.mode,k,g.key),p.return=m,m=p):(k=Yi(g.type,g.key,g.props,null,m.mode,k),k.ref=ho(m,p,g),k.return=m,m=k)}return s(m);case mr:e:{for(C=g.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(m,p.sibling),p=o(p,g.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=_a(g,m.mode,k),p.return=m,m=p}return s(m);case cn:return C=g._init,b(m,p,C(g._payload),k)}if(xo(g))return w(m,p,g,k);if(ao(g))return y(m,p,g,k);Ti(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(m,p.sibling),p=o(p,g),p.return=m,m=p):(n(m,p),p=Ma(g,m.mode,k),p.return=m,m=p),s(m)):n(m,p)}return b}var Qr=Th(!0),Ah=Th(!1),hs=zn(null),ps=null,Er=null,bu=null;function ku(){bu=Er=ps=null}function Su(e){var t=hs.current;de(hs),e._currentValue=t}function xl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Rr(e,t){ps=e,bu=Er=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(bu!==e)if(e={context:e,memoizedValue:t,next:null},Er===null){if(ps===null)throw Error(T(308));Er=e,ps.dependencies={lanes:0,firstContext:e}}else Er=Er.next=e;return t}var Un=null;function Eu(e){Un===null?Un=[e]:Un.push(e)}function Rh(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Eu(t)):(n.next=o.next,o.next=n),t.interleaved=n,Gt(e,r)}function Gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var dn=!1;function Cu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Oh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Pn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Gt(e,n)}return o=r.interleaved,o===null?(t.next=t,Eu(r)):(t.next=o.next,o.next=t),r.interleaved=t,Gt(e,n)}function Ui(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,cu(e,n)}}function td(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ms(e,t,n,r){var o=e.updateQueue;dn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var u=a,c=u.next;u.next=null,s===null?i=c:s.next=c,s=u;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==s&&(a===null?f.firstBaseUpdate=c:a.next=c,f.lastBaseUpdate=u))}if(i!==null){var h=o.baseState;s=0,f=c=u=null,a=i;do{var d=a.lane,v=a.eventTime;if((r&d)===d){f!==null&&(f=f.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,y=a;switch(d=t,v=n,y.tag){case 1:if(w=y.payload,typeof w=="function"){h=w.call(v,h,d);break e}h=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,d=typeof w=="function"?w.call(v,h,d):w,d==null)break e;h=ge({},h,d);break e;case 2:dn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[a]:d.push(a))}else v={eventTime:v,lane:d,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(c=f=v,u=h):f=f.next=v,s|=d;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;d=a,a=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(f===null&&(u=h),o.baseState=u,o.firstBaseUpdate=c,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);tr|=s,e.lanes=s,e.memoizedState=h}}function nd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(T(191,o));o.call(r)}}}var ai={},It=zn(ai),Wo=zn(ai),Uo=zn(ai);function Vn(e){if(e===ai)throw Error(T(174));return e}function Pu(e,t){switch(ae(Uo,t),ae(Wo,e),ae(It,ai),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Za(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Za(t,e)}de(It),ae(It,t)}function Kr(){de(It),de(Wo),de(Uo)}function Lh(e){Vn(Uo.current);var t=Vn(It.current),n=Za(t,e.type);t!==n&&(ae(Wo,e),ae(It,n))}function ju(e){Wo.current===e&&(de(It),de(Wo))}var pe=zn(0);function gs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Na=[];function Nu(){for(var e=0;e<Na.length;e++)Na[e]._workInProgressVersionPrimary=null;Na.length=0}var Vi=Zt.ReactCurrentDispatcher,Ta=Zt.ReactCurrentBatchConfig,er=0,me=null,ke=null,Ce=null,ys=!1,jo=!1,Vo=0,dv=0;function Oe(){throw Error(T(321))}function Tu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!St(e[n],t[n]))return!1;return!0}function Au(e,t,n,r,o,i){if(er=i,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Vi.current=e===null||e.memoizedState===null?mv:gv,e=n(r,o),jo){i=0;do{if(jo=!1,Vo=0,25<=i)throw Error(T(301));i+=1,Ce=ke=null,t.updateQueue=null,Vi.current=yv,e=n(r,o)}while(jo)}if(Vi.current=vs,t=ke!==null&&ke.next!==null,er=0,Ce=ke=me=null,ys=!1,t)throw Error(T(300));return e}function Ru(){var e=Vo!==0;return Vo=0,e}function At(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ce===null?me.memoizedState=Ce=e:Ce=Ce.next=e,Ce}function ht(){if(ke===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=ke.next;var t=Ce===null?me.memoizedState:Ce.next;if(t!==null)Ce=t,ke=e;else{if(e===null)throw Error(T(310));ke=e,e={memoizedState:ke.memoizedState,baseState:ke.baseState,baseQueue:ke.baseQueue,queue:ke.queue,next:null},Ce===null?me.memoizedState=Ce=e:Ce=Ce.next=e}return Ce}function qo(e,t){return typeof t=="function"?t(e):t}function Aa(e){var t=ht(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ke,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,u=null,c=i;do{var f=c.lane;if((er&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=h,s=r):u=u.next=h,me.lanes|=f,tr|=f}c=c.next}while(c!==null&&c!==i);u===null?s=r:u.next=a,St(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,me.lanes|=i,tr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ra(e){var t=ht(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);St(i,t.memoizedState)||(Ve=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Mh(){}function _h(e,t){var n=me,r=ht(),o=t(),i=!St(r.memoizedState,o);if(i&&(r.memoizedState=o,Ve=!0),r=r.queue,Ou(zh.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Ce!==null&&Ce.memoizedState.tag&1){if(n.flags|=2048,Qo(9,Dh.bind(null,n,r,o,t),void 0,null),Pe===null)throw Error(T(349));er&30||Ih(n,t,o)}return o}function Ih(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Dh(e,t,n,r){t.value=n,t.getSnapshot=r,Fh(t)&&Bh(e)}function zh(e,t,n){return n(function(){Fh(t)&&Bh(e)})}function Fh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!St(e,n)}catch{return!0}}function Bh(e){var t=Gt(e,1);t!==null&&kt(t,e,1,-1)}function rd(e){var t=At();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qo,lastRenderedState:e},t.queue=e,e=e.dispatch=pv.bind(null,me,e),[t.memoizedState,e]}function Qo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function $h(){return ht().memoizedState}function qi(e,t,n,r){var o=At();me.flags|=e,o.memoizedState=Qo(1|t,n,void 0,r===void 0?null:r)}function Fs(e,t,n,r){var o=ht();r=r===void 0?null:r;var i=void 0;if(ke!==null){var s=ke.memoizedState;if(i=s.destroy,r!==null&&Tu(r,s.deps)){o.memoizedState=Qo(t,n,i,r);return}}me.flags|=e,o.memoizedState=Qo(1|t,n,i,r)}function od(e,t){return qi(8390656,8,e,t)}function Ou(e,t){return Fs(2048,8,e,t)}function Hh(e,t){return Fs(4,2,e,t)}function Wh(e,t){return Fs(4,4,e,t)}function Uh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Vh(e,t,n){return n=n!=null?n.concat([e]):null,Fs(4,4,Uh.bind(null,t,e),n)}function Lu(){}function qh(e,t){var n=ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Tu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Qh(e,t){var n=ht();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Tu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Kh(e,t,n){return er&21?(St(n,t)||(n=Zf(),me.lanes|=n,tr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function fv(e,t){var n=ie;ie=n!==0&&4>n?n:4,e(!0);var r=Ta.transition;Ta.transition={};try{e(!1),t()}finally{ie=n,Ta.transition=r}}function Gh(){return ht().memoizedState}function hv(e,t,n){var r=Nn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yh(e))Jh(t,n);else if(n=Rh(e,t,n,r),n!==null){var o=$e();kt(n,e,r,o),Xh(n,t,r)}}function pv(e,t,n){var r=Nn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yh(e))Jh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,St(a,s)){var u=t.interleaved;u===null?(o.next=o,Eu(t)):(o.next=u.next,u.next=o),t.interleaved=o;return}}catch{}finally{}n=Rh(e,t,o,r),n!==null&&(o=$e(),kt(n,e,r,o),Xh(n,t,r))}}function Yh(e){var t=e.alternate;return e===me||t!==null&&t===me}function Jh(e,t){jo=ys=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,cu(e,n)}}var vs={readContext:ft,useCallback:Oe,useContext:Oe,useEffect:Oe,useImperativeHandle:Oe,useInsertionEffect:Oe,useLayoutEffect:Oe,useMemo:Oe,useReducer:Oe,useRef:Oe,useState:Oe,useDebugValue:Oe,useDeferredValue:Oe,useTransition:Oe,useMutableSource:Oe,useSyncExternalStore:Oe,useId:Oe,unstable_isNewReconciler:!1},mv={readContext:ft,useCallback:function(e,t){return At().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:od,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qi(4194308,4,Uh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qi(4194308,4,e,t)},useInsertionEffect:function(e,t){return qi(4,2,e,t)},useMemo:function(e,t){var n=At();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=At();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=hv.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=At();return e={current:e},t.memoizedState=e},useState:rd,useDebugValue:Lu,useDeferredValue:function(e){return At().memoizedState=e},useTransition:function(){var e=rd(!1),t=e[0];return e=fv.bind(null,e[1]),At().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,o=At();if(fe){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),Pe===null)throw Error(T(349));er&30||Ih(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,od(zh.bind(null,r,i,e),[e]),r.flags|=2048,Qo(9,Dh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=At(),t=Pe.identifierPrefix;if(fe){var n=Vt,r=Ut;n=(r&~(1<<32-bt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Vo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=dv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},gv={readContext:ft,useCallback:qh,useContext:ft,useEffect:Ou,useImperativeHandle:Vh,useInsertionEffect:Hh,useLayoutEffect:Wh,useMemo:Qh,useReducer:Aa,useRef:$h,useState:function(){return Aa(qo)},useDebugValue:Lu,useDeferredValue:function(e){var t=ht();return Kh(t,ke.memoizedState,e)},useTransition:function(){var e=Aa(qo)[0],t=ht().memoizedState;return[e,t]},useMutableSource:Mh,useSyncExternalStore:_h,useId:Gh,unstable_isNewReconciler:!1},yv={readContext:ft,useCallback:qh,useContext:ft,useEffect:Ou,useImperativeHandle:Vh,useInsertionEffect:Hh,useLayoutEffect:Wh,useMemo:Qh,useReducer:Ra,useRef:$h,useState:function(){return Ra(qo)},useDebugValue:Lu,useDeferredValue:function(e){var t=ht();return ke===null?t.memoizedState=e:Kh(t,ke.memoizedState,e)},useTransition:function(){var e=Ra(qo)[0],t=ht().memoizedState;return[e,t]},useMutableSource:Mh,useSyncExternalStore:_h,useId:Gh,unstable_isNewReconciler:!1};function gt(e,t){if(e&&e.defaultProps){t=ge({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function wl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ge({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Bs={isMounted:function(e){return(e=e._reactInternals)?sr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=$e(),o=Nn(e),i=qt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Pn(e,i,o),t!==null&&(kt(t,e,o,r),Ui(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=$e(),o=Nn(e),i=qt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Pn(e,i,o),t!==null&&(kt(t,e,o,r),Ui(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=$e(),r=Nn(e),o=qt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Pn(e,o,r),t!==null&&(kt(t,e,r,n),Ui(t,e,r))}};function id(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Fo(n,r)||!Fo(o,i):!0}function Zh(e,t,n){var r=!1,o=On,i=t.contextType;return typeof i=="object"&&i!==null?i=ft(i):(o=Qe(t)?Xn:Ie.current,r=t.contextTypes,i=(r=r!=null)?Vr(e,o):On),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Bs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function sd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Bs.enqueueReplaceState(t,t.state,null)}function bl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Cu(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=ft(i):(i=Qe(t)?Xn:Ie.current,o.context=Vr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(wl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Bs.enqueueReplaceState(o,o.state,null),ms(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Gr(e,t){try{var n="",r=t;do n+=Vg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Oa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function kl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var vv=typeof WeakMap=="function"?WeakMap:Map;function ep(e,t,n){n=qt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ws||(ws=!0,Ol=r),kl(e,t)},n}function tp(e,t,n){n=qt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){kl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){kl(e,t),typeof r!="function"&&(jn===null?jn=new Set([this]):jn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function ad(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new vv;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Ov.bind(null,e,t,n),t.then(e,e))}function ld(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ud(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=qt(-1,1),t.tag=2,Pn(n,t,1))),n.lanes|=1),e)}var xv=Zt.ReactCurrentOwner,Ve=!1;function Fe(e,t,n,r){t.child=e===null?Ah(t,null,n,r):Qr(t,e.child,n,r)}function cd(e,t,n,r,o){n=n.render;var i=t.ref;return Rr(t,o),r=Au(e,t,n,r,i,o),n=Ru(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Yt(e,t,o)):(fe&&n&&vu(t),t.flags|=1,Fe(e,t,r,o),t.child)}function dd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!$u(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,np(e,t,i,r,o)):(e=Yi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Fo,n(s,r)&&e.ref===t.ref)return Yt(e,t,o)}return t.flags|=1,e=Tn(i,r),e.ref=t.ref,e.return=t,t.child=e}function np(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Fo(i,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,Yt(e,t,o)}return Sl(e,t,n,r,o)}function rp(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(Pr,Ze),Ze|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(Pr,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ae(Pr,Ze),Ze|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ae(Pr,Ze),Ze|=r;return Fe(e,t,o,n),t.child}function op(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Sl(e,t,n,r,o){var i=Qe(n)?Xn:Ie.current;return i=Vr(t,i),Rr(t,o),n=Au(e,t,n,r,i,o),r=Ru(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Yt(e,t,o)):(fe&&r&&vu(t),t.flags|=1,Fe(e,t,n,o),t.child)}function fd(e,t,n,r,o){if(Qe(n)){var i=!0;cs(t)}else i=!1;if(Rr(t,o),t.stateNode===null)Qi(e,t),Zh(t,n,r),bl(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=ft(c):(c=Qe(n)?Xn:Ie.current,c=Vr(t,c));var f=n.getDerivedStateFromProps,h=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";h||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||u!==c)&&sd(t,s,r,c),dn=!1;var d=t.memoizedState;s.state=d,ms(t,r,s,o),u=t.memoizedState,a!==r||d!==u||qe.current||dn?(typeof f=="function"&&(wl(t,n,f,r),u=t.memoizedState),(a=dn||id(t,n,a,r,d,u,c))?(h||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Oh(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:gt(t.type,a),s.props=c,h=t.pendingProps,d=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=ft(u):(u=Qe(n)?Xn:Ie.current,u=Vr(t,u));var v=n.getDerivedStateFromProps;(f=typeof v=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==h||d!==u)&&sd(t,s,r,u),dn=!1,d=t.memoizedState,s.state=d,ms(t,r,s,o);var w=t.memoizedState;a!==h||d!==w||qe.current||dn?(typeof v=="function"&&(wl(t,n,v,r),w=t.memoizedState),(c=dn||id(t,n,c,r,d,w,u)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,w,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,w,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),s.props=r,s.state=w,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return El(e,t,n,r,i,o)}function El(e,t,n,r,o,i){op(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Jc(t,n,!1),Yt(e,t,i);r=t.stateNode,xv.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Qr(t,e.child,null,i),t.child=Qr(t,null,a,i)):Fe(e,t,a,i),t.memoizedState=r.state,o&&Jc(t,n,!0),t.child}function ip(e){var t=e.stateNode;t.pendingContext?Yc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Yc(e,t.context,!1),Pu(e,t.containerInfo)}function hd(e,t,n,r,o){return qr(),wu(o),t.flags|=256,Fe(e,t,n,r),t.child}var Cl={dehydrated:null,treeContext:null,retryLane:0};function Pl(e){return{baseLanes:e,cachePool:null,transitions:null}}function sp(e,t,n){var r=t.pendingProps,o=pe.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(o&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ae(pe,o&1),e===null)return vl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Ws(s,r,0,null),e=Jn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Pl(n),t.memoizedState=Cl,e):Mu(t,s));if(o=e.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return wv(e,t,s,r,a,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,a=o.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Tn(o,u),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Tn(a,i):(i=Jn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?Pl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Cl,r}return i=e.child,e=i.sibling,r=Tn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Mu(e,t){return t=Ws({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ai(e,t,n,r){return r!==null&&wu(r),Qr(t,e.child,null,n),e=Mu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function wv(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Oa(Error(T(422))),Ai(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ws({mode:"visible",children:r.children},o,0,null),i=Jn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Qr(t,e.child,null,s),t.child.memoizedState=Pl(s),t.memoizedState=Cl,i);if(!(t.mode&1))return Ai(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(T(419)),r=Oa(i,r,void 0),Ai(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ve||a){if(r=Pe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Gt(e,o),kt(r,e,o,-1))}return Bu(),r=Oa(Error(T(421))),Ai(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Lv.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,tt=Cn(o.nextSibling),nt=t,fe=!0,wt=null,e!==null&&(lt[ut++]=Ut,lt[ut++]=Vt,lt[ut++]=Zn,Ut=e.id,Vt=e.overflow,Zn=t),t=Mu(t,r.children),t.flags|=4096,t)}function pd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),xl(e.return,t,n)}function La(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function ap(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Fe(e,t,r.children,n),r=pe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pd(e,n,t);else if(e.tag===19)pd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(pe,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&gs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),La(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&gs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}La(t,!0,n,null,i);break;case"together":La(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Qi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),tr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Tn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bv(e,t,n){switch(t.tag){case 3:ip(t),qr();break;case 5:Lh(t);break;case 1:Qe(t.type)&&cs(t);break;case 4:Pu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ae(hs,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(pe,pe.current&1),t.flags|=128,null):n&t.child.childLanes?sp(e,t,n):(ae(pe,pe.current&1),e=Yt(e,t,n),e!==null?e.sibling:null);ae(pe,pe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ap(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ae(pe,pe.current),r)break;return null;case 22:case 23:return t.lanes=0,rp(e,t,n)}return Yt(e,t,n)}var lp,jl,up,cp;lp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};jl=function(){};up=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Vn(It.current);var i=null;switch(n){case"input":o=Ga(e,o),r=Ga(e,r),i=[];break;case"select":o=ge({},o,{value:void 0}),r=ge({},r,{value:void 0}),i=[];break;case"textarea":o=Xa(e,o),r=Xa(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ls)}el(n,r);var s;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var a=o[c];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Oo.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(a=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(s in a)!a.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&a[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Oo.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&ce("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};cp=function(e,t,n,r){n!==r&&(t.flags|=4)};function po(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function kv(e,t,n){var r=t.pendingProps;switch(xu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Qe(t.type)&&us(),Le(t),null;case 3:return r=t.stateNode,Kr(),de(qe),de(Ie),Nu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ni(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,wt!==null&&(_l(wt),wt=null))),jl(e,t),Le(t),null;case 5:ju(t);var o=Vn(Uo.current);if(n=t.type,e!==null&&t.stateNode!=null)up(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return Le(t),null}if(e=Vn(It.current),Ni(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Mt]=t,r[Ho]=i,e=(t.mode&1)!==0,n){case"dialog":ce("cancel",r),ce("close",r);break;case"iframe":case"object":case"embed":ce("load",r);break;case"video":case"audio":for(o=0;o<bo.length;o++)ce(bo[o],r);break;case"source":ce("error",r);break;case"img":case"image":case"link":ce("error",r),ce("load",r);break;case"details":ce("toggle",r);break;case"input":Sc(r,i),ce("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ce("invalid",r);break;case"textarea":Cc(r,i),ce("invalid",r)}el(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&ji(r.textContent,a,e),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&ji(r.textContent,a,e),o=["children",""+a]):Oo.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&ce("scroll",r)}switch(n){case"input":xi(r),Ec(r,i,!0);break;case"textarea":xi(r),Pc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ls)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=zf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Mt]=t,e[Ho]=r,lp(e,t,!1,!1),t.stateNode=e;e:{switch(s=tl(n,r),n){case"dialog":ce("cancel",e),ce("close",e),o=r;break;case"iframe":case"object":case"embed":ce("load",e),o=r;break;case"video":case"audio":for(o=0;o<bo.length;o++)ce(bo[o],e);o=r;break;case"source":ce("error",e),o=r;break;case"img":case"image":case"link":ce("error",e),ce("load",e),o=r;break;case"details":ce("toggle",e),o=r;break;case"input":Sc(e,r),o=Ga(e,r),ce("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ge({},r,{value:void 0}),ce("invalid",e);break;case"textarea":Cc(e,r),o=Xa(e,r),ce("invalid",e);break;default:o=r}el(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?$f(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ff(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Lo(e,u):typeof u=="number"&&Lo(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Oo.hasOwnProperty(i)?u!=null&&i==="onScroll"&&ce("scroll",e):u!=null&&ou(e,i,u,s))}switch(n){case"input":xi(e),Ec(e,r,!1);break;case"textarea":xi(e),Pc(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Rn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?jr(e,!!r.multiple,i,!1):r.defaultValue!=null&&jr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=ls)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Le(t),null;case 6:if(e&&t.stateNode!=null)cp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=Vn(Uo.current),Vn(It.current),Ni(t)){if(r=t.stateNode,n=t.memoizedProps,r[Mt]=t,(i=r.nodeValue!==n)&&(e=nt,e!==null))switch(e.tag){case 3:ji(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ji(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Mt]=t,t.stateNode=r}return Le(t),null;case 13:if(de(pe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&tt!==null&&t.mode&1&&!(t.flags&128))Nh(),qr(),t.flags|=98560,i=!1;else if(i=Ni(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(T(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(T(317));i[Mt]=t}else qr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Le(t),i=!1}else wt!==null&&(_l(wt),wt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||pe.current&1?Ee===0&&(Ee=3):Bu())),t.updateQueue!==null&&(t.flags|=4),Le(t),null);case 4:return Kr(),jl(e,t),e===null&&Bo(t.stateNode.containerInfo),Le(t),null;case 10:return Su(t.type._context),Le(t),null;case 17:return Qe(t.type)&&us(),Le(t),null;case 19:if(de(pe),i=t.memoizedState,i===null)return Le(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)po(i,!1);else{if(Ee!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=gs(e),s!==null){for(t.flags|=128,po(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(pe,pe.current&1|2),t.child}e=e.sibling}i.tail!==null&&xe()>Yr&&(t.flags|=128,r=!0,po(i,!1),t.lanes=4194304)}else{if(!r)if(e=gs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),po(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!fe)return Le(t),null}else 2*xe()-i.renderingStartTime>Yr&&n!==1073741824&&(t.flags|=128,r=!0,po(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=xe(),t.sibling=null,n=pe.current,ae(pe,r?n&1|2:n&1),t):(Le(t),null);case 22:case 23:return Fu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ze&1073741824&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function Sv(e,t){switch(xu(t),t.tag){case 1:return Qe(t.type)&&us(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kr(),de(qe),de(Ie),Nu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ju(t),null;case 13:if(de(pe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));qr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return de(pe),null;case 4:return Kr(),null;case 10:return Su(t.type._context),null;case 22:case 23:return Fu(),null;case 24:return null;default:return null}}var Ri=!1,_e=!1,Ev=typeof WeakSet=="function"?WeakSet:Set,M=null;function Cr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ve(e,t,r)}else n.current=null}function Nl(e,t,n){try{n()}catch(r){ve(e,t,r)}}var md=!1;function Cv(e,t){if(dl=is,e=mh(),yu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,a=-1,u=-1,c=0,f=0,h=e,d=null;t:for(;;){for(var v;h!==n||o!==0&&h.nodeType!==3||(a=s+o),h!==i||r!==0&&h.nodeType!==3||(u=s+r),h.nodeType===3&&(s+=h.nodeValue.length),(v=h.firstChild)!==null;)d=h,h=v;for(;;){if(h===e)break t;if(d===n&&++c===o&&(a=s),d===i&&++f===r&&(u=s),(v=h.nextSibling)!==null)break;h=d,d=h.parentNode}h=v}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(fl={focusedElem:e,selectionRange:n},is=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var y=w.memoizedProps,b=w.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?y:gt(t.type,y),b);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(k){ve(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return w=md,md=!1,w}function No(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Nl(t,n,i)}o=o.next}while(o!==r)}}function $s(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Tl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function dp(e){var t=e.alternate;t!==null&&(e.alternate=null,dp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Mt],delete t[Ho],delete t[ml],delete t[av],delete t[lv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function fp(e){return e.tag===5||e.tag===3||e.tag===4}function gd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||fp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Al(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ls));else if(r!==4&&(e=e.child,e!==null))for(Al(e,t,n),e=e.sibling;e!==null;)Al(e,t,n),e=e.sibling}function Rl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Rl(e,t,n),e=e.sibling;e!==null;)Rl(e,t,n),e=e.sibling}var Ne=null,xt=!1;function sn(e,t,n){for(n=n.child;n!==null;)hp(e,t,n),n=n.sibling}function hp(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(Ls,n)}catch{}switch(n.tag){case 5:_e||Cr(n,t);case 6:var r=Ne,o=xt;Ne=null,sn(e,t,n),Ne=r,xt=o,Ne!==null&&(xt?(e=Ne,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ne.removeChild(n.stateNode));break;case 18:Ne!==null&&(xt?(e=Ne,n=n.stateNode,e.nodeType===8?Pa(e.parentNode,n):e.nodeType===1&&Pa(e,n),Do(e)):Pa(Ne,n.stateNode));break;case 4:r=Ne,o=xt,Ne=n.stateNode.containerInfo,xt=!0,sn(e,t,n),Ne=r,xt=o;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Nl(n,t,s),o=o.next}while(o!==r)}sn(e,t,n);break;case 1:if(!_e&&(Cr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ve(n,t,a)}sn(e,t,n);break;case 21:sn(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,sn(e,t,n),_e=r):sn(e,t,n);break;default:sn(e,t,n)}}function yd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Ev),t.forEach(function(r){var o=Mv.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function mt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Ne=a.stateNode,xt=!1;break e;case 3:Ne=a.stateNode.containerInfo,xt=!0;break e;case 4:Ne=a.stateNode.containerInfo,xt=!0;break e}a=a.return}if(Ne===null)throw Error(T(160));hp(i,s,o),Ne=null,xt=!1;var u=o.alternate;u!==null&&(u.return=null),o.return=null}catch(c){ve(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)pp(t,e),t=t.sibling}function pp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mt(t,e),Tt(e),r&4){try{No(3,e,e.return),$s(3,e)}catch(y){ve(e,e.return,y)}try{No(5,e,e.return)}catch(y){ve(e,e.return,y)}}break;case 1:mt(t,e),Tt(e),r&512&&n!==null&&Cr(n,n.return);break;case 5:if(mt(t,e),Tt(e),r&512&&n!==null&&Cr(n,n.return),e.flags&32){var o=e.stateNode;try{Lo(o,"")}catch(y){ve(e,e.return,y)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&If(o,i),tl(a,s);var c=tl(a,i);for(s=0;s<u.length;s+=2){var f=u[s],h=u[s+1];f==="style"?$f(o,h):f==="dangerouslySetInnerHTML"?Ff(o,h):f==="children"?Lo(o,h):ou(o,f,h,c)}switch(a){case"input":Ya(o,i);break;case"textarea":Df(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?jr(o,!!i.multiple,v,!1):d!==!!i.multiple&&(i.defaultValue!=null?jr(o,!!i.multiple,i.defaultValue,!0):jr(o,!!i.multiple,i.multiple?[]:"",!1))}o[Ho]=i}catch(y){ve(e,e.return,y)}}break;case 6:if(mt(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(T(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(y){ve(e,e.return,y)}}break;case 3:if(mt(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Do(t.containerInfo)}catch(y){ve(e,e.return,y)}break;case 4:mt(t,e),Tt(e);break;case 13:mt(t,e),Tt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Du=xe())),r&4&&yd(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(c=_e)||f,mt(t,e),_e=c):mt(t,e),Tt(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(M=e,f=e.child;f!==null;){for(h=M=f;M!==null;){switch(d=M,v=d.child,d.tag){case 0:case 11:case 14:case 15:No(4,d,d.return);break;case 1:Cr(d,d.return);var w=d.stateNode;if(typeof w.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(y){ve(r,n,y)}}break;case 5:Cr(d,d.return);break;case 22:if(d.memoizedState!==null){xd(h);continue}}v!==null?(v.return=d,M=v):xd(h)}f=f.sibling}e:for(f=null,h=e;;){if(h.tag===5){if(f===null){f=h;try{o=h.stateNode,c?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=h.stateNode,u=h.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Bf("display",s))}catch(y){ve(e,e.return,y)}}}else if(h.tag===6){if(f===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(y){ve(e,e.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;f===h&&(f=null),h=h.return}f===h&&(f=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:mt(t,e),Tt(e),r&4&&yd(e);break;case 21:break;default:mt(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(fp(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Lo(o,""),r.flags&=-33);var i=gd(e);Rl(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=gd(e);Al(e,a,s);break;default:throw Error(T(161))}}catch(u){ve(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Pv(e,t,n){M=e,mp(e)}function mp(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var o=M,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ri;if(!s){var a=o.alternate,u=a!==null&&a.memoizedState!==null||_e;a=Ri;var c=_e;if(Ri=s,(_e=u)&&!c)for(M=o;M!==null;)s=M,u=s.child,s.tag===22&&s.memoizedState!==null?wd(o):u!==null?(u.return=s,M=u):wd(o);for(;i!==null;)M=i,mp(i),i=i.sibling;M=o,Ri=a,_e=c}vd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,M=i):vd(e)}}function vd(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||$s(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:gt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&nd(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}nd(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var h=f.dehydrated;h!==null&&Do(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}_e||t.flags&512&&Tl(t)}catch(d){ve(t,t.return,d)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function xd(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function wd(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{$s(4,t)}catch(u){ve(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(u){ve(t,o,u)}}var i=t.return;try{Tl(t)}catch(u){ve(t,i,u)}break;case 5:var s=t.return;try{Tl(t)}catch(u){ve(t,s,u)}}}catch(u){ve(t,t.return,u)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var jv=Math.ceil,xs=Zt.ReactCurrentDispatcher,_u=Zt.ReactCurrentOwner,dt=Zt.ReactCurrentBatchConfig,X=0,Pe=null,we=null,Te=0,Ze=0,Pr=zn(0),Ee=0,Ko=null,tr=0,Hs=0,Iu=0,To=null,Ue=null,Du=0,Yr=1/0,Ht=null,ws=!1,Ol=null,jn=null,Oi=!1,wn=null,bs=0,Ao=0,Ll=null,Ki=-1,Gi=0;function $e(){return X&6?xe():Ki!==-1?Ki:Ki=xe()}function Nn(e){return e.mode&1?X&2&&Te!==0?Te&-Te:cv.transition!==null?(Gi===0&&(Gi=Zf()),Gi):(e=ie,e!==0||(e=window.event,e=e===void 0?16:sh(e.type)),e):1}function kt(e,t,n,r){if(50<Ao)throw Ao=0,Ll=null,Error(T(185));oi(e,n,r),(!(X&2)||e!==Pe)&&(e===Pe&&(!(X&2)&&(Hs|=n),Ee===4&&hn(e,Te)),Ke(e,r),n===1&&X===0&&!(t.mode&1)&&(Yr=xe()+500,zs&&Fn()))}function Ke(e,t){var n=e.callbackNode;cy(e,t);var r=os(e,e===Pe?Te:0);if(r===0)n!==null&&Tc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Tc(n),t===1)e.tag===0?uv(bd.bind(null,e)):Ch(bd.bind(null,e)),iv(function(){!(X&6)&&Fn()}),n=null;else{switch(eh(r)){case 1:n=uu;break;case 4:n=Jf;break;case 16:n=rs;break;case 536870912:n=Xf;break;default:n=rs}n=Sp(n,gp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function gp(e,t){if(Ki=-1,Gi=0,X&6)throw Error(T(327));var n=e.callbackNode;if(Or()&&e.callbackNode!==n)return null;var r=os(e,e===Pe?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ks(e,r);else{t=r;var o=X;X|=2;var i=vp();(Pe!==e||Te!==t)&&(Ht=null,Yr=xe()+500,Yn(e,t));do try{Av();break}catch(a){yp(e,a)}while(!0);ku(),xs.current=i,X=o,we!==null?t=0:(Pe=null,Te=0,t=Ee)}if(t!==0){if(t===2&&(o=sl(e),o!==0&&(r=o,t=Ml(e,o))),t===1)throw n=Ko,Yn(e,0),hn(e,r),Ke(e,xe()),n;if(t===6)hn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Nv(o)&&(t=ks(e,r),t===2&&(i=sl(e),i!==0&&(r=i,t=Ml(e,i))),t===1))throw n=Ko,Yn(e,0),hn(e,r),Ke(e,xe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Hn(e,Ue,Ht);break;case 3:if(hn(e,r),(r&130023424)===r&&(t=Du+500-xe(),10<t)){if(os(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){$e(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=pl(Hn.bind(null,e,Ue,Ht),t);break}Hn(e,Ue,Ht);break;case 4:if(hn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-bt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=xe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jv(r/1960))-r,10<r){e.timeoutHandle=pl(Hn.bind(null,e,Ue,Ht),r);break}Hn(e,Ue,Ht);break;case 5:Hn(e,Ue,Ht);break;default:throw Error(T(329))}}}return Ke(e,xe()),e.callbackNode===n?gp.bind(null,e):null}function Ml(e,t){var n=To;return e.current.memoizedState.isDehydrated&&(Yn(e,t).flags|=256),e=ks(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&_l(t)),e}function _l(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function Nv(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!St(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function hn(e,t){for(t&=~Iu,t&=~Hs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-bt(t),r=1<<n;e[n]=-1,t&=~r}}function bd(e){if(X&6)throw Error(T(327));Or();var t=os(e,0);if(!(t&1))return Ke(e,xe()),null;var n=ks(e,t);if(e.tag!==0&&n===2){var r=sl(e);r!==0&&(t=r,n=Ml(e,r))}if(n===1)throw n=Ko,Yn(e,0),hn(e,t),Ke(e,xe()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Hn(e,Ue,Ht),Ke(e,xe()),null}function zu(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(Yr=xe()+500,zs&&Fn())}}function nr(e){wn!==null&&wn.tag===0&&!(X&6)&&Or();var t=X;X|=1;var n=dt.transition,r=ie;try{if(dt.transition=null,ie=1,e)return e()}finally{ie=r,dt.transition=n,X=t,!(X&6)&&Fn()}}function Fu(){Ze=Pr.current,de(Pr)}function Yn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ov(n)),we!==null)for(n=we.return;n!==null;){var r=n;switch(xu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&us();break;case 3:Kr(),de(qe),de(Ie),Nu();break;case 5:ju(r);break;case 4:Kr();break;case 13:de(pe);break;case 19:de(pe);break;case 10:Su(r.type._context);break;case 22:case 23:Fu()}n=n.return}if(Pe=e,we=e=Tn(e.current,null),Te=Ze=t,Ee=0,Ko=null,Iu=Hs=tr=0,Ue=To=null,Un!==null){for(t=0;t<Un.length;t++)if(n=Un[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Un=null}return e}function yp(e,t){do{var n=we;try{if(ku(),Vi.current=vs,ys){for(var r=me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ys=!1}if(er=0,Ce=ke=me=null,jo=!1,Vo=0,_u.current=null,n===null||n.return===null){Ee=1,Ko=t,we=null;break}e:{var i=e,s=n.return,a=n,u=t;if(t=Te,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=a,h=f.tag;if(!(f.mode&1)&&(h===0||h===11||h===15)){var d=f.alternate;d?(f.updateQueue=d.updateQueue,f.memoizedState=d.memoizedState,f.lanes=d.lanes):(f.updateQueue=null,f.memoizedState=null)}var v=ld(s);if(v!==null){v.flags&=-257,ud(v,s,a,i,t),v.mode&1&&ad(i,c,t),t=v,u=c;var w=t.updateQueue;if(w===null){var y=new Set;y.add(u),t.updateQueue=y}else w.add(u);break e}else{if(!(t&1)){ad(i,c,t),Bu();break e}u=Error(T(426))}}else if(fe&&a.mode&1){var b=ld(s);if(b!==null){!(b.flags&65536)&&(b.flags|=256),ud(b,s,a,i,t),wu(Gr(u,a));break e}}i=u=Gr(u,a),Ee!==4&&(Ee=2),To===null?To=[i]:To.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=ep(i,u,t);td(i,m);break e;case 1:a=u;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(jn===null||!jn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var k=tp(i,a,t);td(i,k);break e}}i=i.return}while(i!==null)}wp(n)}catch(S){t=S,we===n&&n!==null&&(we=n=n.return);continue}break}while(!0)}function vp(){var e=xs.current;return xs.current=vs,e===null?vs:e}function Bu(){(Ee===0||Ee===3||Ee===2)&&(Ee=4),Pe===null||!(tr&268435455)&&!(Hs&268435455)||hn(Pe,Te)}function ks(e,t){var n=X;X|=2;var r=vp();(Pe!==e||Te!==t)&&(Ht=null,Yn(e,t));do try{Tv();break}catch(o){yp(e,o)}while(!0);if(ku(),X=n,xs.current=r,we!==null)throw Error(T(261));return Pe=null,Te=0,Ee}function Tv(){for(;we!==null;)xp(we)}function Av(){for(;we!==null&&!ty();)xp(we)}function xp(e){var t=kp(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?wp(e):we=t,_u.current=null}function wp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Sv(n,t),n!==null){n.flags&=32767,we=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ee=6,we=null;return}}else if(n=kv(n,t,Ze),n!==null){we=n;return}if(t=t.sibling,t!==null){we=t;return}we=t=e}while(t!==null);Ee===0&&(Ee=5)}function Hn(e,t,n){var r=ie,o=dt.transition;try{dt.transition=null,ie=1,Rv(e,t,n,r)}finally{dt.transition=o,ie=r}return null}function Rv(e,t,n,r){do Or();while(wn!==null);if(X&6)throw Error(T(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(dy(e,i),e===Pe&&(we=Pe=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Oi||(Oi=!0,Sp(rs,function(){return Or(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=dt.transition,dt.transition=null;var s=ie;ie=1;var a=X;X|=4,_u.current=null,Cv(e,n),pp(n,e),Jy(fl),is=!!dl,fl=dl=null,e.current=n,Pv(n),ny(),X=a,ie=s,dt.transition=i}else e.current=n;if(Oi&&(Oi=!1,wn=e,bs=o),i=e.pendingLanes,i===0&&(jn=null),iy(n.stateNode),Ke(e,xe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ws)throw ws=!1,e=Ol,Ol=null,e;return bs&1&&e.tag!==0&&Or(),i=e.pendingLanes,i&1?e===Ll?Ao++:(Ao=0,Ll=e):Ao=0,Fn(),null}function Or(){if(wn!==null){var e=eh(bs),t=dt.transition,n=ie;try{if(dt.transition=null,ie=16>e?16:e,wn===null)var r=!1;else{if(e=wn,wn=null,bs=0,X&6)throw Error(T(331));var o=X;for(X|=4,M=e.current;M!==null;){var i=M,s=i.child;if(M.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(M=c;M!==null;){var f=M;switch(f.tag){case 0:case 11:case 15:No(8,f,i)}var h=f.child;if(h!==null)h.return=f,M=h;else for(;M!==null;){f=M;var d=f.sibling,v=f.return;if(dp(f),f===c){M=null;break}if(d!==null){d.return=v,M=d;break}M=v}}}var w=i.alternate;if(w!==null){var y=w.child;if(y!==null){w.child=null;do{var b=y.sibling;y.sibling=null,y=b}while(y!==null)}}M=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,M=s;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:No(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,M=m;break e}M=i.return}}var p=e.current;for(M=p;M!==null;){s=M;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,M=g;else e:for(s=p;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:$s(9,a)}}catch(S){ve(a,a.return,S)}if(a===s){M=null;break e}var k=a.sibling;if(k!==null){k.return=a.return,M=k;break e}M=a.return}}if(X=o,Fn(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(Ls,e)}catch{}r=!0}return r}finally{ie=n,dt.transition=t}}return!1}function kd(e,t,n){t=Gr(n,t),t=ep(e,t,1),e=Pn(e,t,1),t=$e(),e!==null&&(oi(e,1,t),Ke(e,t))}function ve(e,t,n){if(e.tag===3)kd(e,e,n);else for(;t!==null;){if(t.tag===3){kd(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(jn===null||!jn.has(r))){e=Gr(n,e),e=tp(t,e,1),t=Pn(t,e,1),e=$e(),t!==null&&(oi(t,1,e),Ke(t,e));break}}t=t.return}}function Ov(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=$e(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Te&n)===n&&(Ee===4||Ee===3&&(Te&130023424)===Te&&500>xe()-Du?Yn(e,0):Iu|=n),Ke(e,t)}function bp(e,t){t===0&&(e.mode&1?(t=ki,ki<<=1,!(ki&130023424)&&(ki=4194304)):t=1);var n=$e();e=Gt(e,t),e!==null&&(oi(e,t,n),Ke(e,n))}function Lv(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bp(e,n)}function Mv(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),bp(e,n)}var kp;kp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||qe.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,bv(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,fe&&t.flags&1048576&&Ph(t,fs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Qi(e,t),e=t.pendingProps;var o=Vr(t,Ie.current);Rr(t,n),o=Au(null,t,r,e,o,n);var i=Ru();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(r)?(i=!0,cs(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Cu(t),o.updater=Bs,t.stateNode=o,o._reactInternals=t,bl(t,r,e,n),t=El(null,t,r,!0,i,n)):(t.tag=0,fe&&i&&vu(t),Fe(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Qi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Iv(r),e=gt(r,e),o){case 0:t=Sl(null,t,r,e,n);break e;case 1:t=fd(null,t,r,e,n);break e;case 11:t=cd(null,t,r,e,n);break e;case 14:t=dd(null,t,r,gt(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),Sl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),fd(e,t,r,o,n);case 3:e:{if(ip(t),e===null)throw Error(T(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Oh(e,t),ms(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Gr(Error(T(423)),t),t=hd(e,t,r,n,o);break e}else if(r!==o){o=Gr(Error(T(424)),t),t=hd(e,t,r,n,o);break e}else for(tt=Cn(t.stateNode.containerInfo.firstChild),nt=t,fe=!0,wt=null,n=Ah(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(qr(),r===o){t=Yt(e,t,n);break e}Fe(e,t,r,n)}t=t.child}return t;case 5:return Lh(t),e===null&&vl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,hl(r,o)?s=null:i!==null&&hl(r,i)&&(t.flags|=32),op(e,t),Fe(e,t,s,n),t.child;case 6:return e===null&&vl(t),null;case 13:return sp(e,t,n);case 4:return Pu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Qr(t,null,r,n):Fe(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),cd(e,t,r,o,n);case 7:return Fe(e,t,t.pendingProps,n),t.child;case 8:return Fe(e,t,t.pendingProps.children,n),t.child;case 12:return Fe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,ae(hs,r._currentValue),r._currentValue=s,i!==null)if(St(i.value,s)){if(i.children===o.children&&!qe.current){t=Yt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=qt(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),xl(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(T(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),xl(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Fe(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Rr(t,n),o=ft(o),r=r(o),t.flags|=1,Fe(e,t,r,n),t.child;case 14:return r=t.type,o=gt(r,t.pendingProps),o=gt(r.type,o),dd(e,t,r,o,n);case 15:return np(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:gt(r,o),Qi(e,t),t.tag=1,Qe(r)?(e=!0,cs(t)):e=!1,Rr(t,n),Zh(t,r,o),bl(t,r,o,n),El(null,t,r,!0,e,n);case 19:return ap(e,t,n);case 22:return rp(e,t,n)}throw Error(T(156,t.tag))};function Sp(e,t){return Yf(e,t)}function _v(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ct(e,t,n,r){return new _v(e,t,n,r)}function $u(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Iv(e){if(typeof e=="function")return $u(e)?1:0;if(e!=null){if(e=e.$$typeof,e===su)return 11;if(e===au)return 14}return 2}function Tn(e,t){var n=e.alternate;return n===null?(n=ct(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Yi(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")$u(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case gr:return Jn(n.children,o,i,t);case iu:s=8,o|=8;break;case Va:return e=ct(12,n,t,o|2),e.elementType=Va,e.lanes=i,e;case qa:return e=ct(13,n,t,o),e.elementType=qa,e.lanes=i,e;case Qa:return e=ct(19,n,t,o),e.elementType=Qa,e.lanes=i,e;case Lf:return Ws(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Rf:s=10;break e;case Of:s=9;break e;case su:s=11;break e;case au:s=14;break e;case cn:s=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=ct(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Jn(e,t,n,r){return e=ct(7,e,r,t),e.lanes=n,e}function Ws(e,t,n,r){return e=ct(22,e,r,t),e.elementType=Lf,e.lanes=n,e.stateNode={isHidden:!1},e}function Ma(e,t,n){return e=ct(6,e,null,t),e.lanes=n,e}function _a(e,t,n){return t=ct(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Dv(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ma(0),this.expirationTimes=ma(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ma(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Hu(e,t,n,r,o,i,s,a,u){return e=new Dv(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ct(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Cu(i),e}function zv(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:mr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ep(e){if(!e)return On;e=e._reactInternals;e:{if(sr(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Qe(n))return Eh(e,n,t)}return t}function Cp(e,t,n,r,o,i,s,a,u){return e=Hu(n,r,!0,e,o,i,s,a,u),e.context=Ep(null),n=e.current,r=$e(),o=Nn(n),i=qt(r,o),i.callback=t??null,Pn(n,i,o),e.current.lanes=o,oi(e,o,r),Ke(e,r),e}function Us(e,t,n,r){var o=t.current,i=$e(),s=Nn(o);return n=Ep(n),t.context===null?t.context=n:t.pendingContext=n,t=qt(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Pn(o,t,s),e!==null&&(kt(e,o,s,i),Ui(e,o,s)),s}function Ss(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Sd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Wu(e,t){Sd(e,t),(e=e.alternate)&&Sd(e,t)}function Fv(){return null}var Pp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Uu(e){this._internalRoot=e}Vs.prototype.render=Uu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));Us(e,t,null,null)};Vs.prototype.unmount=Uu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;nr(function(){Us(null,e,null,null)}),t[Kt]=null}};function Vs(e){this._internalRoot=e}Vs.prototype.unstable_scheduleHydration=function(e){if(e){var t=rh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<fn.length&&t!==0&&t<fn[n].priority;n++);fn.splice(n,0,e),n===0&&ih(e)}};function Vu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ed(){}function Bv(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var c=Ss(s);i.call(c)}}var s=Cp(t,r,e,0,null,!1,!1,"",Ed);return e._reactRootContainer=s,e[Kt]=s.current,Bo(e.nodeType===8?e.parentNode:e),nr(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var c=Ss(u);a.call(c)}}var u=Hu(e,0,!1,null,null,!1,!1,"",Ed);return e._reactRootContainer=u,e[Kt]=u.current,Bo(e.nodeType===8?e.parentNode:e),nr(function(){Us(t,u,n,r)}),u}function Qs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var u=Ss(s);a.call(u)}}Us(t,s,e,o)}else s=Bv(n,t,e,o,r);return Ss(s)}th=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=wo(t.pendingLanes);n!==0&&(cu(t,n|1),Ke(t,xe()),!(X&6)&&(Yr=xe()+500,Fn()))}break;case 13:nr(function(){var r=Gt(e,1);if(r!==null){var o=$e();kt(r,e,1,o)}}),Wu(e,1)}};du=function(e){if(e.tag===13){var t=Gt(e,134217728);if(t!==null){var n=$e();kt(t,e,134217728,n)}Wu(e,134217728)}};nh=function(e){if(e.tag===13){var t=Nn(e),n=Gt(e,t);if(n!==null){var r=$e();kt(n,e,t,r)}Wu(e,t)}};rh=function(){return ie};oh=function(e,t){var n=ie;try{return ie=e,t()}finally{ie=n}};rl=function(e,t,n){switch(t){case"input":if(Ya(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ds(r);if(!o)throw Error(T(90));_f(r),Ya(r,o)}}}break;case"textarea":Df(e,n);break;case"select":t=n.value,t!=null&&jr(e,!!n.multiple,t,!1)}};Uf=zu;Vf=nr;var $v={usingClientEntryPoint:!1,Events:[si,wr,Ds,Hf,Wf,zu]},mo={findFiberByHostInstance:Wn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Hv={bundleType:mo.bundleType,version:mo.version,rendererPackageName:mo.rendererPackageName,rendererConfig:mo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Zt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Kf(e),e===null?null:e.stateNode},findFiberByHostInstance:mo.findFiberByHostInstance||Fv,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Li=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Li.isDisabled&&Li.supportsFiber)try{Ls=Li.inject(Hv),_t=Li}catch{}}it.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=$v;it.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Vu(t))throw Error(T(200));return zv(e,t,null,n)};it.createRoot=function(e,t){if(!Vu(e))throw Error(T(299));var n=!1,r="",o=Pp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Hu(e,1,!1,null,null,n,!1,r,o),e[Kt]=t.current,Bo(e.nodeType===8?e.parentNode:e),new Uu(t)};it.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Kf(t),e=e===null?null:e.stateNode,e};it.flushSync=function(e){return nr(e)};it.hydrate=function(e,t,n){if(!qs(t))throw Error(T(200));return Qs(null,e,t,!0,n)};it.hydrateRoot=function(e,t,n){if(!Vu(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Pp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Cp(t,null,e,1,n??null,o,!1,i,s),e[Kt]=t.current,Bo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Vs(t)};it.render=function(e,t,n){if(!qs(t))throw Error(T(200));return Qs(null,e,t,!1,n)};it.unmountComponentAtNode=function(e){if(!qs(e))throw Error(T(40));return e._reactRootContainer?(nr(function(){Qs(null,null,e,!1,function(){e._reactRootContainer=null,e[Kt]=null})}),!0):!1};it.unstable_batchedUpdates=zu;it.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!qs(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return Qs(e,t,n,!1,r)};it.version="18.3.1-next-f1338f8080-20240426";function jp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(jp)}catch(e){console.error(e)}}jp(),jf.exports=it;var li=jf.exports;const Np=mf(li);var Tp,Cd=li;Tp=Cd.createRoot,Cd.hydrateRoot;const Wv=1,Uv=1e6;let Ia=0;function Vv(){return Ia=(Ia+1)%Number.MAX_SAFE_INTEGER,Ia.toString()}const Da=new Map,Pd=e=>{if(Da.has(e))return;const t=setTimeout(()=>{Da.delete(e),Ro({type:"REMOVE_TOAST",toastId:e})},Uv);Da.set(e,t)},qv=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Wv)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?Pd(n):e.toasts.forEach(r=>{Pd(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Ji=[];let Xi={toasts:[]};function Ro(e){Xi=qv(Xi,e),Ji.forEach(t=>{t(Xi)})}function Qv({...e}){const t=Vv(),n=o=>Ro({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Ro({type:"DISMISS_TOAST",toastId:t});return Ro({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function Kv(){const[e,t]=x.useState(Xi);return x.useEffect(()=>(Ji.push(t),()=>{const n=Ji.indexOf(t);n>-1&&Ji.splice(n,1)}),[e]),{...e,toast:Qv,dismiss:n=>Ro({type:"DISMISS_TOAST",toastId:n})}}function Se(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Gv(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Ap(...e){return t=>e.forEach(n=>Gv(n,t))}function Et(...e){return x.useCallback(Ap(...e),e)}function Yv(e,t=[]){let n=[];function r(i,s){const a=x.createContext(s),u=n.length;n=[...n,s];function c(h){const{scope:d,children:v,...w}=h,y=(d==null?void 0:d[e][u])||a,b=x.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:b,children:v})}function f(h,d){const v=(d==null?void 0:d[e][u])||a,w=x.useContext(v);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,f]}const o=()=>{const i=n.map(s=>x.createContext(s));return function(a){const u=(a==null?void 0:a[e])||i;return x.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return o.scopeName=e,[r,Jv(o,...t)]}function Jv(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:u,scopeName:c})=>{const h=u(i)[`__scope${c}`];return{...a,...h}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Es=x.forwardRef((e,t)=>{const{children:n,...r}=e,o=x.Children.toArray(n),i=o.find(Xv);if(i){const s=i.props.children,a=o.map(u=>u===i?x.Children.count(s)>1?x.Children.only(null):x.isValidElement(s)?s.props.children:null:u);return l.jsx(Il,{...r,ref:t,children:x.isValidElement(s)?x.cloneElement(s,void 0,a):null})}return l.jsx(Il,{...r,ref:t,children:n})});Es.displayName="Slot";var Il=x.forwardRef((e,t)=>{const{children:n,...r}=e;if(x.isValidElement(n)){const o=ex(n);return x.cloneElement(n,{...Zv(r,n.props),ref:t?Ap(t,o):o})}return x.Children.count(n)>1?x.Children.only(null):null});Il.displayName="SlotClone";var Rp=({children:e})=>l.jsx(l.Fragment,{children:e});function Xv(e){return x.isValidElement(e)&&e.type===Rp}function Zv(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...a)=>{i(...a),o(...a)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function ex(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function tx(e){const t=e+"CollectionProvider",[n,r]=Yv(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=v=>{const{scope:w,children:y}=v,b=R.useRef(null),m=R.useRef(new Map).current;return l.jsx(o,{scope:w,itemMap:m,collectionRef:b,children:y})};s.displayName=t;const a=e+"CollectionSlot",u=R.forwardRef((v,w)=>{const{scope:y,children:b}=v,m=i(a,y),p=Et(w,m.collectionRef);return l.jsx(Es,{ref:p,children:b})});u.displayName=a;const c=e+"CollectionItemSlot",f="data-radix-collection-item",h=R.forwardRef((v,w)=>{const{scope:y,children:b,...m}=v,p=R.useRef(null),g=Et(w,p),k=i(c,y);return R.useEffect(()=>(k.itemMap.set(p,{ref:p,...m}),()=>void k.itemMap.delete(p))),l.jsx(Es,{[f]:"",ref:g,children:b})});h.displayName=c;function d(v){const w=i(e+"CollectionConsumer",v);return R.useCallback(()=>{const b=w.collectionRef.current;if(!b)return[];const m=Array.from(b.querySelectorAll(`[${f}]`));return Array.from(w.itemMap.values()).sort((k,S)=>m.indexOf(k.ref.current)-m.indexOf(S.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:s,Slot:u,ItemSlot:h},d,r]}function Op(e,t=[]){let n=[];function r(i,s){const a=x.createContext(s),u=n.length;n=[...n,s];const c=h=>{var m;const{scope:d,children:v,...w}=h,y=((m=d==null?void 0:d[e])==null?void 0:m[u])||a,b=x.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:b,children:v})};c.displayName=i+"Provider";function f(h,d){var y;const v=((y=d==null?void 0:d[e])==null?void 0:y[u])||a,w=x.useContext(v);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return[c,f]}const o=()=>{const i=n.map(s=>x.createContext(s));return function(a){const u=(a==null?void 0:a[e])||i;return x.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return o.scopeName=e,[r,nx(o,...t)]}function nx(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:u,scopeName:c})=>{const h=u(i)[`__scope${c}`];return{...a,...h}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var rx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ge=rx.reduce((e,t)=>{const n=x.forwardRef((r,o)=>{const{asChild:i,...s}=r,a=i?Es:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(a,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Lp(e,t){e&&li.flushSync(()=>e.dispatchEvent(t))}function Dt(e){const t=x.useRef(e);return x.useEffect(()=>{t.current=e}),x.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function ox(e,t=globalThis==null?void 0:globalThis.document){const n=Dt(e);x.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var ix="DismissableLayer",Dl="dismissableLayer.update",sx="dismissableLayer.pointerDownOutside",ax="dismissableLayer.focusOutside",jd,Mp=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),qu=x.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:a,...u}=e,c=x.useContext(Mp),[f,h]=x.useState(null),d=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=x.useState({}),w=Et(t,P=>h(P)),y=Array.from(c.layers),[b]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),m=y.indexOf(b),p=f?y.indexOf(f):-1,g=c.layersWithOutsidePointerEventsDisabled.size>0,k=p>=m,S=ux(P=>{const N=P.target,I=[...c.branches].some(O=>O.contains(N));!k||I||(o==null||o(P),s==null||s(P),P.defaultPrevented||a==null||a())},d),C=cx(P=>{const N=P.target;[...c.branches].some(O=>O.contains(N))||(i==null||i(P),s==null||s(P),P.defaultPrevented||a==null||a())},d);return ox(P=>{p===c.layers.size-1&&(r==null||r(P),!P.defaultPrevented&&a&&(P.preventDefault(),a()))},d),x.useEffect(()=>{if(f)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(jd=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(f)),c.layers.add(f),Nd(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=jd)}},[f,d,n,c]),x.useEffect(()=>()=>{f&&(c.layers.delete(f),c.layersWithOutsidePointerEventsDisabled.delete(f),Nd())},[f,c]),x.useEffect(()=>{const P=()=>v({});return document.addEventListener(Dl,P),()=>document.removeEventListener(Dl,P)},[]),l.jsx(Ge.div,{...u,ref:w,style:{pointerEvents:g?k?"auto":"none":void 0,...e.style},onFocusCapture:Se(e.onFocusCapture,C.onFocusCapture),onBlurCapture:Se(e.onBlurCapture,C.onBlurCapture),onPointerDownCapture:Se(e.onPointerDownCapture,S.onPointerDownCapture)})});qu.displayName=ix;var lx="DismissableLayerBranch",_p=x.forwardRef((e,t)=>{const n=x.useContext(Mp),r=x.useRef(null),o=Et(t,r);return x.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),l.jsx(Ge.div,{...e,ref:o})});_p.displayName=lx;function ux(e,t=globalThis==null?void 0:globalThis.document){const n=Dt(e),r=x.useRef(!1),o=x.useRef(()=>{});return x.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let u=function(){Ip(sx,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function cx(e,t=globalThis==null?void 0:globalThis.document){const n=Dt(e),r=x.useRef(!1);return x.useEffect(()=>{const o=i=>{i.target&&!r.current&&Ip(ax,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Nd(){const e=new CustomEvent(Dl);document.dispatchEvent(e)}function Ip(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Lp(o,i):o.dispatchEvent(i)}var dx=qu,fx=_p,rr=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},hx="Portal",Dp=x.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,i]=x.useState(!1);rr(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Np.createPortal(l.jsx(Ge.div,{...r,ref:t}),s):null});Dp.displayName=hx;function px(e,t){return x.useReducer((n,r)=>t[n][r]??n,e)}var Qu=e=>{const{present:t,children:n}=e,r=mx(t),o=typeof n=="function"?n({present:r.isPresent}):x.Children.only(n),i=Et(r.ref,gx(o));return typeof n=="function"||r.isPresent?x.cloneElement(o,{ref:i}):null};Qu.displayName="Presence";function mx(e){const[t,n]=x.useState(),r=x.useRef({}),o=x.useRef(e),i=x.useRef("none"),s=e?"mounted":"unmounted",[a,u]=px(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const c=Mi(r.current);i.current=a==="mounted"?c:"none"},[a]),rr(()=>{const c=r.current,f=o.current;if(f!==e){const d=i.current,v=Mi(c);e?u("MOUNT"):v==="none"||(c==null?void 0:c.display)==="none"?u("UNMOUNT"):u(f&&d!==v?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),rr(()=>{if(t){let c;const f=t.ownerDocument.defaultView??window,h=v=>{const y=Mi(r.current).includes(v.animationName);if(v.target===t&&y&&(u("ANIMATION_END"),!o.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",c=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},d=v=>{v.target===t&&(i.current=Mi(r.current))};return t.addEventListener("animationstart",d),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{f.clearTimeout(c),t.removeEventListener("animationstart",d),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:x.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function Mi(e){return(e==null?void 0:e.animationName)||"none"}function gx(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function yx({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=vx({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,a=Dt(n),u=x.useCallback(c=>{if(i){const h=typeof c=="function"?c(e):c;h!==e&&a(h)}else o(c)},[i,e,o,a]);return[s,u]}function vx({defaultProp:e,onChange:t}){const n=x.useState(e),[r]=n,o=x.useRef(r),i=Dt(t);return x.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var xx="VisuallyHidden",Ks=x.forwardRef((e,t)=>l.jsx(Ge.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Ks.displayName=xx;var wx=Ks,Ku="ToastProvider",[Gu,bx,kx]=tx("Toast"),[zp,Hk]=Op("Toast",[kx]),[Sx,Gs]=zp(Ku),Fp=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[a,u]=x.useState(null),[c,f]=x.useState(0),h=x.useRef(!1),d=x.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Ku}\`. Expected non-empty \`string\`.`),l.jsx(Gu.Provider,{scope:t,children:l.jsx(Sx,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:c,viewport:a,onViewportChange:u,onToastAdd:x.useCallback(()=>f(v=>v+1),[]),onToastRemove:x.useCallback(()=>f(v=>v-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:d,children:s})})};Fp.displayName=Ku;var Bp="ToastViewport",Ex=["F8"],zl="toast.viewportPause",Fl="toast.viewportResume",$p=x.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=Ex,label:o="Notifications ({hotkey})",...i}=e,s=Gs(Bp,n),a=bx(n),u=x.useRef(null),c=x.useRef(null),f=x.useRef(null),h=x.useRef(null),d=Et(t,h,s.onViewportChange),v=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=s.toastCount>0;x.useEffect(()=>{const b=m=>{var g;r.length!==0&&r.every(k=>m[k]||m.code===k)&&((g=h.current)==null||g.focus())};return document.addEventListener("keydown",b),()=>document.removeEventListener("keydown",b)},[r]),x.useEffect(()=>{const b=u.current,m=h.current;if(w&&b&&m){const p=()=>{if(!s.isClosePausedRef.current){const C=new CustomEvent(zl);m.dispatchEvent(C),s.isClosePausedRef.current=!0}},g=()=>{if(s.isClosePausedRef.current){const C=new CustomEvent(Fl);m.dispatchEvent(C),s.isClosePausedRef.current=!1}},k=C=>{!b.contains(C.relatedTarget)&&g()},S=()=>{b.contains(document.activeElement)||g()};return b.addEventListener("focusin",p),b.addEventListener("focusout",k),b.addEventListener("pointermove",p),b.addEventListener("pointerleave",S),window.addEventListener("blur",p),window.addEventListener("focus",g),()=>{b.removeEventListener("focusin",p),b.removeEventListener("focusout",k),b.removeEventListener("pointermove",p),b.removeEventListener("pointerleave",S),window.removeEventListener("blur",p),window.removeEventListener("focus",g)}}},[w,s.isClosePausedRef]);const y=x.useCallback(({tabbingDirection:b})=>{const p=a().map(g=>{const k=g.ref.current,S=[k,...Dx(k)];return b==="forwards"?S:S.reverse()});return(b==="forwards"?p.reverse():p).flat()},[a]);return x.useEffect(()=>{const b=h.current;if(b){const m=p=>{var S,C,P;const g=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!g){const N=document.activeElement,I=p.shiftKey;if(p.target===b&&I){(S=c.current)==null||S.focus();return}const D=y({tabbingDirection:I?"backwards":"forwards"}),V=D.findIndex(L=>L===N);za(D.slice(V+1))?p.preventDefault():I?(C=c.current)==null||C.focus():(P=f.current)==null||P.focus()}};return b.addEventListener("keydown",m),()=>b.removeEventListener("keydown",m)}},[a,y]),l.jsxs(fx,{ref:u,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&l.jsx(Bl,{ref:c,onFocusFromOutsideViewport:()=>{const b=y({tabbingDirection:"forwards"});za(b)}}),l.jsx(Gu.Slot,{scope:n,children:l.jsx(Ge.ol,{tabIndex:-1,...i,ref:d})}),w&&l.jsx(Bl,{ref:f,onFocusFromOutsideViewport:()=>{const b=y({tabbingDirection:"backwards"});za(b)}})]})});$p.displayName=Bp;var Hp="ToastFocusProxy",Bl=x.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=Gs(Hp,n);return l.jsx(Ks,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var c;const a=s.relatedTarget;!((c=i.viewport)!=null&&c.contains(a))&&r()}})});Bl.displayName=Hp;var Ys="Toast",Cx="toast.swipeStart",Px="toast.swipeMove",jx="toast.swipeCancel",Nx="toast.swipeEnd",Wp=x.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[a=!0,u]=yx({prop:r,defaultProp:o,onChange:i});return l.jsx(Qu,{present:n||a,children:l.jsx(Rx,{open:a,...s,ref:t,onClose:()=>u(!1),onPause:Dt(e.onPause),onResume:Dt(e.onResume),onSwipeStart:Se(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Se(e.onSwipeMove,c=>{const{x:f,y:h}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${h}px`)}),onSwipeCancel:Se(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Se(e.onSwipeEnd,c=>{const{x:f,y:h}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${h}px`),u(!1)})})})});Wp.displayName=Ys;var[Tx,Ax]=zp(Ys,{onClose(){}}),Rx=x.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:a,onPause:u,onResume:c,onSwipeStart:f,onSwipeMove:h,onSwipeCancel:d,onSwipeEnd:v,...w}=e,y=Gs(Ys,n),[b,m]=x.useState(null),p=Et(t,L=>m(L)),g=x.useRef(null),k=x.useRef(null),S=o||y.duration,C=x.useRef(0),P=x.useRef(S),N=x.useRef(0),{onToastAdd:I,onToastRemove:O}=y,B=Dt(()=>{var q;(b==null?void 0:b.contains(document.activeElement))&&((q=y.viewport)==null||q.focus()),s()}),D=x.useCallback(L=>{!L||L===1/0||(window.clearTimeout(N.current),C.current=new Date().getTime(),N.current=window.setTimeout(B,L))},[B]);x.useEffect(()=>{const L=y.viewport;if(L){const q=()=>{D(P.current),c==null||c()},$=()=>{const Q=new Date().getTime()-C.current;P.current=P.current-Q,window.clearTimeout(N.current),u==null||u()};return L.addEventListener(zl,$),L.addEventListener(Fl,q),()=>{L.removeEventListener(zl,$),L.removeEventListener(Fl,q)}}},[y.viewport,S,u,c,D]),x.useEffect(()=>{i&&!y.isClosePausedRef.current&&D(S)},[i,S,y.isClosePausedRef,D]),x.useEffect(()=>(I(),()=>O()),[I,O]);const V=x.useMemo(()=>b?Yp(b):null,[b]);return y.viewport?l.jsxs(l.Fragment,{children:[V&&l.jsx(Ox,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:V}),l.jsx(Tx,{scope:n,onClose:B,children:li.createPortal(l.jsx(Gu.ItemSlot,{scope:n,children:l.jsx(dx,{asChild:!0,onEscapeKeyDown:Se(a,()=>{y.isFocusedToastEscapeKeyDownRef.current||B(),y.isFocusedToastEscapeKeyDownRef.current=!1}),children:l.jsx(Ge.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":y.swipeDirection,...w,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Se(e.onKeyDown,L=>{L.key==="Escape"&&(a==null||a(L.nativeEvent),L.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:Se(e.onPointerDown,L=>{L.button===0&&(g.current={x:L.clientX,y:L.clientY})}),onPointerMove:Se(e.onPointerMove,L=>{if(!g.current)return;const q=L.clientX-g.current.x,$=L.clientY-g.current.y,Q=!!k.current,E=["left","right"].includes(y.swipeDirection),A=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,z=E?A(0,q):0,_=E?0:A(0,$),F=L.pointerType==="touch"?10:2,Y={x:z,y:_},he={originalEvent:L,delta:Y};Q?(k.current=Y,_i(Px,h,he,{discrete:!1})):Td(Y,y.swipeDirection,F)?(k.current=Y,_i(Cx,f,he,{discrete:!1}),L.target.setPointerCapture(L.pointerId)):(Math.abs(q)>F||Math.abs($)>F)&&(g.current=null)}),onPointerUp:Se(e.onPointerUp,L=>{const q=k.current,$=L.target;if($.hasPointerCapture(L.pointerId)&&$.releasePointerCapture(L.pointerId),k.current=null,g.current=null,q){const Q=L.currentTarget,E={originalEvent:L,delta:q};Td(q,y.swipeDirection,y.swipeThreshold)?_i(Nx,v,E,{discrete:!0}):_i(jx,d,E,{discrete:!0}),Q.addEventListener("click",A=>A.preventDefault(),{once:!0})}})})})}),y.viewport)})]}):null}),Ox=e=>{const{__scopeToast:t,children:n,...r}=e,o=Gs(Ys,t),[i,s]=x.useState(!1),[a,u]=x.useState(!1);return _x(()=>s(!0)),x.useEffect(()=>{const c=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(c)},[]),a?null:l.jsx(Dp,{asChild:!0,children:l.jsx(Ks,{...r,children:i&&l.jsxs(l.Fragment,{children:[o.label," ",n]})})})},Lx="ToastTitle",Up=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(Ge.div,{...r,ref:t})});Up.displayName=Lx;var Mx="ToastDescription",Vp=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return l.jsx(Ge.div,{...r,ref:t})});Vp.displayName=Mx;var qp="ToastAction",Qp=x.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?l.jsx(Gp,{altText:n,asChild:!0,children:l.jsx(Yu,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${qp}\`. Expected non-empty \`string\`.`),null)});Qp.displayName=qp;var Kp="ToastClose",Yu=x.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=Ax(Kp,n);return l.jsx(Gp,{asChild:!0,children:l.jsx(Ge.button,{type:"button",...r,ref:t,onClick:Se(e.onClick,o.onClose)})})});Yu.displayName=Kp;var Gp=x.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return l.jsx(Ge.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Yp(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),Ix(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Yp(r))}}),t}function _i(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Lp(o,i):o.dispatchEvent(i)}var Td=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function _x(e=()=>{}){const t=Dt(e);rr(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function Ix(e){return e.nodeType===e.ELEMENT_NODE}function Dx(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function za(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var zx=Fp,Jp=$p,Xp=Wp,Zp=Up,em=Vp,tm=Qp,nm=Yu;function rm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=rm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function om(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=rm(e))&&(r&&(r+=" "),r+=t);return r}const Ad=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Rd=om,Fx=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Rd(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(c=>{const f=n==null?void 0:n[c],h=i==null?void 0:i[c];if(f===null)return null;const d=Ad(f)||Ad(h);return o[c][d]}),a=n&&Object.entries(n).reduce((c,f)=>{let[h,d]=f;return d===void 0||(c[h]=d),c},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,f)=>{let{class:h,className:d,...v}=f;return Object.entries(v).every(w=>{let[y,b]=w;return Array.isArray(b)?b.includes({...i,...a}[y]):{...i,...a}[y]===b})?[...c,h,d]:c},[]);return Rd(e,s,u,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bx=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),im=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var $x={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hx=x.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...a},u)=>x.createElement("svg",{ref:u,...$x,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:im("lucide",o),...a},[...s.map(([c,f])=>x.createElement(c,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ju=(e,t)=>{const n=x.forwardRef(({className:r,...o},i)=>x.createElement(Hx,{ref:i,iconNode:t,className:im(`lucide-${Bx(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wx=Ju("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sm=Ju("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const am=Ju("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Xu="-",Ux=e=>{const t=qx(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const a=s.split(Xu);return a[0]===""&&a.length!==1&&a.shift(),lm(a,t)||Vx(s)},getConflictingClassGroupIds:(s,a)=>{const u=n[s]||[];return a&&r[s]?[...u,...r[s]]:u}}},lm=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?lm(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Xu);return(s=t.validators.find(({validator:a})=>a(i)))==null?void 0:s.classGroupId},Od=/^\[(.+)\]$/,Vx=e=>{if(Od.test(e)){const t=Od.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},qx=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Kx(Object.entries(e.classGroups),n).forEach(([i,s])=>{$l(s,r,i,t)}),r},$l=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:Ld(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(Qx(o)){$l(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{$l(s,Ld(t,i),n,r)})})},Ld=(e,t)=>{let n=e;return t.split(Xu).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Qx=e=>e.isThemeGetter,Kx=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,a])=>[t+s,a])):i);return[n,o]}):e,Gx=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},um="!",Yx=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=a=>{const u=[];let c=0,f=0,h;for(let b=0;b<a.length;b++){let m=a[b];if(c===0){if(m===o&&(r||a.slice(b,b+i)===t)){u.push(a.slice(f,b)),f=b+i;continue}if(m==="/"){h=b;continue}}m==="["?c++:m==="]"&&c--}const d=u.length===0?a:a.substring(f),v=d.startsWith(um),w=v?d.substring(1):d,y=h&&h>f?h-f:void 0;return{modifiers:u,hasImportantModifier:v,baseClassName:w,maybePostfixModifierPosition:y}};return n?a=>n({className:a,parseClassName:s}):s},Jx=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Xx=e=>({cache:Gx(e.cacheSize),parseClassName:Yx(e),...Ux(e)}),Zx=/\s+/,ew=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(Zx);let a="";for(let u=s.length-1;u>=0;u-=1){const c=s[u],{modifiers:f,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:v}=n(c);let w=!!v,y=r(w?d.substring(0,v):d);if(!y){if(!w){a=c+(a.length>0?" "+a:a);continue}if(y=r(d),!y){a=c+(a.length>0?" "+a:a);continue}w=!1}const b=Jx(f).join(":"),m=h?b+um:b,p=m+y;if(i.includes(p))continue;i.push(p);const g=o(y,w);for(let k=0;k<g.length;++k){const S=g[k];i.push(m+S)}a=c+(a.length>0?" "+a:a)}return a};function tw(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=cm(t))&&(r&&(r+=" "),r+=n);return r}const cm=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=cm(e[r]))&&(n&&(n+=" "),n+=t);return n};function nw(e,...t){let n,r,o,i=s;function s(u){const c=t.reduce((f,h)=>h(f),e());return n=Xx(c),r=n.cache.get,o=n.cache.set,i=a,a(u)}function a(u){const c=r(u);if(c)return c;const f=ew(u,n);return o(u,f),f}return function(){return i(tw.apply(null,arguments))}}const ue=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},dm=/^\[(?:([a-z-]+):)?(.+)\]$/i,rw=/^\d+\/\d+$/,ow=new Set(["px","full","screen"]),iw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sw=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,aw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,lw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,uw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Bt=e=>Lr(e)||ow.has(e)||rw.test(e),an=e=>no(e,"length",yw),Lr=e=>!!e&&!Number.isNaN(Number(e)),Fa=e=>no(e,"number",Lr),go=e=>!!e&&Number.isInteger(Number(e)),cw=e=>e.endsWith("%")&&Lr(e.slice(0,-1)),W=e=>dm.test(e),ln=e=>iw.test(e),dw=new Set(["length","size","percentage"]),fw=e=>no(e,dw,fm),hw=e=>no(e,"position",fm),pw=new Set(["image","url"]),mw=e=>no(e,pw,xw),gw=e=>no(e,"",vw),yo=()=>!0,no=(e,t,n)=>{const r=dm.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},yw=e=>sw.test(e)&&!aw.test(e),fm=()=>!1,vw=e=>lw.test(e),xw=e=>uw.test(e),ww=()=>{const e=ue("colors"),t=ue("spacing"),n=ue("blur"),r=ue("brightness"),o=ue("borderColor"),i=ue("borderRadius"),s=ue("borderSpacing"),a=ue("borderWidth"),u=ue("contrast"),c=ue("grayscale"),f=ue("hueRotate"),h=ue("invert"),d=ue("gap"),v=ue("gradientColorStops"),w=ue("gradientColorStopPositions"),y=ue("inset"),b=ue("margin"),m=ue("opacity"),p=ue("padding"),g=ue("saturate"),k=ue("scale"),S=ue("sepia"),C=ue("skew"),P=ue("space"),N=ue("translate"),I=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",W,t],D=()=>[W,t],V=()=>["",Bt,an],L=()=>["auto",Lr,W],q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],$=()=>["solid","dashed","dotted","double","none"],Q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],E=()=>["start","end","center","between","around","evenly","stretch"],A=()=>["","0",W],z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[Lr,W];return{cacheSize:500,separator:":",theme:{colors:[yo],spacing:[Bt,an],blur:["none","",ln,W],brightness:_(),borderColor:[e],borderRadius:["none","","full",ln,W],borderSpacing:D(),borderWidth:V(),contrast:_(),grayscale:A(),hueRotate:_(),invert:A(),gap:D(),gradientColorStops:[e],gradientColorStopPositions:[cw,an],inset:B(),margin:B(),opacity:_(),padding:D(),saturate:_(),scale:_(),sepia:A(),skew:_(),space:D(),translate:D()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[ln]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...q(),W]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:I()}],"overscroll-x":[{"overscroll-x":I()}],"overscroll-y":[{"overscroll-y":I()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",go,W]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",go,W]}],"grid-cols":[{"grid-cols":[yo]}],"col-start-end":[{col:["auto",{span:["full",go,W]},W]}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":[yo]}],"row-start-end":[{row:["auto",{span:[go,W]},W]}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...E()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...E(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...E(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,t]}],"min-w":[{"min-w":[W,t,"min","max","fit"]}],"max-w":[{"max-w":[W,t,"none","full","min","max","fit","prose",{screen:[ln]},ln]}],h:[{h:[W,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,t,"auto","min","max","fit"]}],"font-size":[{text:["base",ln,an]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Fa]}],"font-family":[{font:[yo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",Lr,Fa]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Bt,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...$(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Bt,an]}],"underline-offset":[{"underline-offset":["auto",Bt,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:D()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...q(),hw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",fw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},mw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[v]}],"gradient-via":[{via:[v]}],"gradient-to":[{to:[v]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...$(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:$()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...$()]}],"outline-offset":[{"outline-offset":[Bt,W]}],"outline-w":[{outline:[Bt,an]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[Bt,an]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",ln,gw]}],"shadow-color":[{shadow:[yo]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...Q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":Q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",ln,W]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[h]}],saturate:[{saturate:[g]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[k]}],"scale-x":[{"scale-x":[k]}],"scale-y":[{"scale-y":[k]}],rotate:[{rotate:[go,W]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":D()}],"scroll-mx":[{"scroll-mx":D()}],"scroll-my":[{"scroll-my":D()}],"scroll-ms":[{"scroll-ms":D()}],"scroll-me":[{"scroll-me":D()}],"scroll-mt":[{"scroll-mt":D()}],"scroll-mr":[{"scroll-mr":D()}],"scroll-mb":[{"scroll-mb":D()}],"scroll-ml":[{"scroll-ml":D()}],"scroll-p":[{"scroll-p":D()}],"scroll-px":[{"scroll-px":D()}],"scroll-py":[{"scroll-py":D()}],"scroll-ps":[{"scroll-ps":D()}],"scroll-pe":[{"scroll-pe":D()}],"scroll-pt":[{"scroll-pt":D()}],"scroll-pr":[{"scroll-pr":D()}],"scroll-pb":[{"scroll-pb":D()}],"scroll-pl":[{"scroll-pl":D()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Bt,an,Fa]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},bw=nw(ww);function ar(...e){return bw(om(e))}const kw=zx,hm=x.forwardRef(({className:e,...t},n)=>l.jsx(Jp,{ref:n,className:ar("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));hm.displayName=Jp.displayName;const Sw=Fx("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),pm=x.forwardRef(({className:e,variant:t,...n},r)=>l.jsx(Xp,{ref:r,className:ar(Sw({variant:t}),e),...n}));pm.displayName=Xp.displayName;const Ew=x.forwardRef(({className:e,...t},n)=>l.jsx(tm,{ref:n,className:ar("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Ew.displayName=tm.displayName;const mm=x.forwardRef(({className:e,...t},n)=>l.jsx(nm,{ref:n,className:ar("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:l.jsx(am,{className:"h-4 w-4"})}));mm.displayName=nm.displayName;const gm=x.forwardRef(({className:e,...t},n)=>l.jsx(Zp,{ref:n,className:ar("text-sm font-semibold",e),...t}));gm.displayName=Zp.displayName;const ym=x.forwardRef(({className:e,...t},n)=>l.jsx(em,{ref:n,className:ar("text-sm opacity-90",e),...t}));ym.displayName=em.displayName;function Cw(){const{toasts:e}=Kv();return l.jsxs(kw,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return l.jsxs(pm,{...i,children:[l.jsxs("div",{className:"grid gap-1",children:[n&&l.jsx(gm,{children:n}),r&&l.jsx(ym,{children:r})]}),o,l.jsx(mm,{})]},t)}),l.jsx(hm,{})]})}var Md=["light","dark"],Pw="(prefers-color-scheme: dark)",jw=x.createContext(void 0),Nw={setTheme:e=>{},themes:[]},Tw=()=>{var e;return(e=x.useContext(jw))!=null?e:Nw};x.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:a,nonce:u})=>{let c=i==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${a.map(w=>`'${w}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,h=o?Md.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(w,y=!1,b=!0)=>{let m=s?s[w]:w,p=y?w+"|| ''":`'${m}'`,g="";return o&&b&&!y&&Md.includes(w)&&(g+=`d.style.colorScheme = '${w}';`),n==="class"?y||m?g+=`c.add(${p})`:g+="null":m&&(g+=`d[s](n,${p})`),g},v=e?`!function(){${f}${d(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${c})){var t='${Pw}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${d(s?"x[e]":"e",!0)}}${c?"":"else{"+d(i,!1,!1)+"}"}${h}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${d(s?"x[e]":"e",!0)}}else{${d(i,!1,!1)};}${h}}catch(t){}}();`;return x.createElement("script",{nonce:u,dangerouslySetInnerHTML:{__html:v}})});var Aw=e=>{switch(e){case"success":return Lw;case"info":return _w;case"warning":return Mw;case"error":return Iw;default:return null}},Rw=Array(12).fill(0),Ow=({visible:e})=>R.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},R.createElement("div",{className:"sonner-spinner"},Rw.map((t,n)=>R.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),Lw=R.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},R.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),Mw=R.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},R.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),_w=R.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},R.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Iw=R.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},R.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Dw=()=>{let[e,t]=R.useState(document.hidden);return R.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Hl=1,zw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Hl++,i=this.toasts.find(a=>a.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(a=>a.id===o?(this.publish({...a,...e,id:o,title:n}),{...a,...e,id:o,dismissible:s,title:n}):a):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(Bw(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,a=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:a})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:a})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,a=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:a})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Hl++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Xe=new zw,Fw=(e,t)=>{let n=(t==null?void 0:t.id)||Hl++;return Xe.addToast({title:e,...t,id:n}),n},Bw=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",$w=Fw,Hw=()=>Xe.toasts;Object.assign($w,{success:Xe.success,info:Xe.info,warning:Xe.warning,error:Xe.error,custom:Xe.custom,message:Xe.message,promise:Xe.promise,dismiss:Xe.dismiss,loading:Xe.loading},{getHistory:Hw});function Ww(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Ww(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Ii(e){return e.label!==void 0}var Uw=3,Vw="32px",qw=4e3,Qw=356,Kw=14,Gw=20,Yw=200;function Jw(...e){return e.filter(Boolean).join(" ")}var Xw=e=>{var t,n,r,o,i,s,a,u,c,f;let{invert:h,toast:d,unstyled:v,interacting:w,setHeights:y,visibleToasts:b,heights:m,index:p,toasts:g,expanded:k,removeToast:S,defaultRichColors:C,closeButton:P,style:N,cancelButtonStyle:I,actionButtonStyle:O,className:B="",descriptionClassName:D="",duration:V,position:L,gap:q,loadingIcon:$,expandByDefault:Q,classNames:E,icons:A,closeButtonAriaLabel:z="Close toast",pauseWhenPageIsHidden:_,cn:F}=e,[Y,he]=R.useState(!1),[Ye,oe]=R.useState(!1),[pt,en]=R.useState(!1),[tn,nn]=R.useState(!1),[fi,cr]=R.useState(0),[Bn,so]=R.useState(0),hi=R.useRef(null),rn=R.useRef(null),ia=p===0,sa=p+1<=b,je=d.type,dr=d.dismissible!==!1,ug=d.className||"",cg=d.descriptionClassName||"",pi=R.useMemo(()=>m.findIndex(H=>H.toastId===d.id)||0,[m,d.id]),dg=R.useMemo(()=>{var H;return(H=d.closeButton)!=null?H:P},[d.closeButton,P]),dc=R.useMemo(()=>d.duration||V||qw,[d.duration,V]),aa=R.useRef(0),fr=R.useRef(0),fc=R.useRef(0),hr=R.useRef(null),[hc,fg]=L.split("-"),pc=R.useMemo(()=>m.reduce((H,le,se)=>se>=pi?H:H+le.height,0),[m,pi]),mc=Dw(),hg=d.invert||h,la=je==="loading";fr.current=R.useMemo(()=>pi*q+pc,[pi,pc]),R.useEffect(()=>{he(!0)},[]),R.useLayoutEffect(()=>{if(!Y)return;let H=rn.current,le=H.style.height;H.style.height="auto";let se=H.getBoundingClientRect().height;H.style.height=le,so(se),y(jt=>jt.find(Nt=>Nt.toastId===d.id)?jt.map(Nt=>Nt.toastId===d.id?{...Nt,height:se}:Nt):[{toastId:d.id,height:se,position:d.position},...jt])},[Y,d.title,d.description,y,d.id]);let on=R.useCallback(()=>{oe(!0),cr(fr.current),y(H=>H.filter(le=>le.toastId!==d.id)),setTimeout(()=>{S(d)},Yw)},[d,S,y,fr]);R.useEffect(()=>{if(d.promise&&je==="loading"||d.duration===1/0||d.type==="loading")return;let H,le=dc;return k||w||_&&mc?(()=>{if(fc.current<aa.current){let se=new Date().getTime()-aa.current;le=le-se}fc.current=new Date().getTime()})():le!==1/0&&(aa.current=new Date().getTime(),H=setTimeout(()=>{var se;(se=d.onAutoClose)==null||se.call(d,d),on()},le)),()=>clearTimeout(H)},[k,w,Q,d,dc,on,d.promise,je,_,mc]),R.useEffect(()=>{let H=rn.current;if(H){let le=H.getBoundingClientRect().height;return so(le),y(se=>[{toastId:d.id,height:le,position:d.position},...se]),()=>y(se=>se.filter(jt=>jt.toastId!==d.id))}},[y,d.id]),R.useEffect(()=>{d.delete&&on()},[on,d.delete]);function pg(){return A!=null&&A.loading?R.createElement("div",{className:"sonner-loader","data-visible":je==="loading"},A.loading):$?R.createElement("div",{className:"sonner-loader","data-visible":je==="loading"},$):R.createElement(Ow,{visible:je==="loading"})}return R.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:rn,className:F(B,ug,E==null?void 0:E.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,E==null?void 0:E.default,E==null?void 0:E[je],(n=d==null?void 0:d.classNames)==null?void 0:n[je]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:C,"data-styled":!(d.jsx||d.unstyled||v),"data-mounted":Y,"data-promise":!!d.promise,"data-removed":Ye,"data-visible":sa,"data-y-position":hc,"data-x-position":fg,"data-index":p,"data-front":ia,"data-swiping":pt,"data-dismissible":dr,"data-type":je,"data-invert":hg,"data-swipe-out":tn,"data-expanded":!!(k||Q&&Y),style:{"--index":p,"--toasts-before":p,"--z-index":g.length-p,"--offset":`${Ye?fi:fr.current}px`,"--initial-height":Q?"auto":`${Bn}px`,...N,...d.style},onPointerDown:H=>{la||!dr||(hi.current=new Date,cr(fr.current),H.target.setPointerCapture(H.pointerId),H.target.tagName!=="BUTTON"&&(en(!0),hr.current={x:H.clientX,y:H.clientY}))},onPointerUp:()=>{var H,le,se,jt;if(tn||!dr)return;hr.current=null;let Nt=Number(((H=rn.current)==null?void 0:H.style.getPropertyValue("--swipe-amount").replace("px",""))||0),mi=new Date().getTime()-((le=hi.current)==null?void 0:le.getTime()),mg=Math.abs(Nt)/mi;if(Math.abs(Nt)>=Gw||mg>.11){cr(fr.current),(se=d.onDismiss)==null||se.call(d,d),on(),nn(!0);return}(jt=rn.current)==null||jt.style.setProperty("--swipe-amount","0px"),en(!1)},onPointerMove:H=>{var le;if(!hr.current||!dr)return;let se=H.clientY-hr.current.y,jt=H.clientX-hr.current.x,Nt=(hc==="top"?Math.min:Math.max)(0,se),mi=H.pointerType==="touch"?10:2;Math.abs(Nt)>mi?(le=rn.current)==null||le.style.setProperty("--swipe-amount",`${se}px`):Math.abs(jt)>mi&&(hr.current=null)}},dg&&!d.jsx?R.createElement("button",{"aria-label":z,"data-disabled":la,"data-close-button":!0,onClick:la||!dr?()=>{}:()=>{var H;on(),(H=d.onDismiss)==null||H.call(d,d)},className:F(E==null?void 0:E.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},R.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},R.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),R.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||R.isValidElement(d.title)?d.jsx||d.title:R.createElement(R.Fragment,null,je||d.icon||d.promise?R.createElement("div",{"data-icon":"",className:F(E==null?void 0:E.icon,(i=d==null?void 0:d.classNames)==null?void 0:i.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||pg():null,d.type!=="loading"?d.icon||(A==null?void 0:A[je])||Aw(je):null):null,R.createElement("div",{"data-content":"",className:F(E==null?void 0:E.content,(s=d==null?void 0:d.classNames)==null?void 0:s.content)},R.createElement("div",{"data-title":"",className:F(E==null?void 0:E.title,(a=d==null?void 0:d.classNames)==null?void 0:a.title)},d.title),d.description?R.createElement("div",{"data-description":"",className:F(D,cg,E==null?void 0:E.description,(u=d==null?void 0:d.classNames)==null?void 0:u.description)},d.description):null),R.isValidElement(d.cancel)?d.cancel:d.cancel&&Ii(d.cancel)?R.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||I,onClick:H=>{var le,se;Ii(d.cancel)&&dr&&((se=(le=d.cancel).onClick)==null||se.call(le,H),on())},className:F(E==null?void 0:E.cancelButton,(c=d==null?void 0:d.classNames)==null?void 0:c.cancelButton)},d.cancel.label):null,R.isValidElement(d.action)?d.action:d.action&&Ii(d.action)?R.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||O,onClick:H=>{var le,se;Ii(d.action)&&(H.defaultPrevented||((se=(le=d.action).onClick)==null||se.call(le,H),on()))},className:F(E==null?void 0:E.actionButton,(f=d==null?void 0:d.classNames)==null?void 0:f.actionButton)},d.action.label):null))};function _d(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Zw=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:a,theme:u="light",richColors:c,duration:f,style:h,visibleToasts:d=Uw,toastOptions:v,dir:w=_d(),gap:y=Kw,loadingIcon:b,icons:m,containerAriaLabel:p="Notifications",pauseWhenPageIsHidden:g,cn:k=Jw}=e,[S,C]=R.useState([]),P=R.useMemo(()=>Array.from(new Set([n].concat(S.filter(_=>_.position).map(_=>_.position)))),[S,n]),[N,I]=R.useState([]),[O,B]=R.useState(!1),[D,V]=R.useState(!1),[L,q]=R.useState(u!=="system"?u:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),$=R.useRef(null),Q=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=R.useRef(null),A=R.useRef(!1),z=R.useCallback(_=>{var F;(F=S.find(Y=>Y.id===_.id))!=null&&F.delete||Xe.dismiss(_.id),C(Y=>Y.filter(({id:he})=>he!==_.id))},[S]);return R.useEffect(()=>Xe.subscribe(_=>{if(_.dismiss){C(F=>F.map(Y=>Y.id===_.id?{...Y,delete:!0}:Y));return}setTimeout(()=>{Np.flushSync(()=>{C(F=>{let Y=F.findIndex(he=>he.id===_.id);return Y!==-1?[...F.slice(0,Y),{...F[Y],..._},...F.slice(Y+1)]:[_,...F]})})})}),[]),R.useEffect(()=>{if(u!=="system"){q(u);return}u==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?q("dark"):q("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:_})=>{q(_?"dark":"light")})},[u]),R.useEffect(()=>{S.length<=1&&B(!1)},[S]),R.useEffect(()=>{let _=F=>{var Y,he;r.every(Ye=>F[Ye]||F.code===Ye)&&(B(!0),(Y=$.current)==null||Y.focus()),F.code==="Escape"&&(document.activeElement===$.current||(he=$.current)!=null&&he.contains(document.activeElement))&&B(!1)};return document.addEventListener("keydown",_),()=>document.removeEventListener("keydown",_)},[r]),R.useEffect(()=>{if($.current)return()=>{E.current&&(E.current.focus({preventScroll:!0}),E.current=null,A.current=!1)}},[$.current]),S.length?R.createElement("section",{"aria-label":`${p} ${Q}`,tabIndex:-1},P.map((_,F)=>{var Y;let[he,Ye]=_.split("-");return R.createElement("ol",{key:_,dir:w==="auto"?_d():w,tabIndex:-1,ref:$,className:s,"data-sonner-toaster":!0,"data-theme":L,"data-y-position":he,"data-x-position":Ye,style:{"--front-toast-height":`${((Y=N[0])==null?void 0:Y.height)||0}px`,"--offset":typeof a=="number"?`${a}px`:a||Vw,"--width":`${Qw}px`,"--gap":`${y}px`,...h},onBlur:oe=>{A.current&&!oe.currentTarget.contains(oe.relatedTarget)&&(A.current=!1,E.current&&(E.current.focus({preventScroll:!0}),E.current=null))},onFocus:oe=>{oe.target instanceof HTMLElement&&oe.target.dataset.dismissible==="false"||A.current||(A.current=!0,E.current=oe.relatedTarget)},onMouseEnter:()=>B(!0),onMouseMove:()=>B(!0),onMouseLeave:()=>{D||B(!1)},onPointerDown:oe=>{oe.target instanceof HTMLElement&&oe.target.dataset.dismissible==="false"||V(!0)},onPointerUp:()=>V(!1)},S.filter(oe=>!oe.position&&F===0||oe.position===_).map((oe,pt)=>{var en,tn;return R.createElement(Xw,{key:oe.id,icons:m,index:pt,toast:oe,defaultRichColors:c,duration:(en=v==null?void 0:v.duration)!=null?en:f,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:t,visibleToasts:d,closeButton:(tn=v==null?void 0:v.closeButton)!=null?tn:i,interacting:D,position:_,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,removeToast:z,toasts:S.filter(nn=>nn.position==oe.position),heights:N.filter(nn=>nn.position==oe.position),setHeights:I,expandByDefault:o,gap:y,loadingIcon:b,expanded:O,pauseWhenPageIsHidden:g,cn:k})}))})):null};const e0=({...e})=>{const{theme:t="system"}=Tw();return l.jsx(Zw,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},t0=["top","right","bottom","left"],Ln=Math.min,et=Math.max,Cs=Math.round,Di=Math.floor,Mn=e=>({x:e,y:e}),n0={left:"right",right:"left",bottom:"top",top:"bottom"},r0={start:"end",end:"start"};function Wl(e,t,n){return et(e,Ln(t,n))}function Jt(e,t){return typeof e=="function"?e(t):e}function Xt(e){return e.split("-")[0]}function ro(e){return e.split("-")[1]}function Zu(e){return e==="x"?"y":"x"}function ec(e){return e==="y"?"height":"width"}function _n(e){return["top","bottom"].includes(Xt(e))?"y":"x"}function tc(e){return Zu(_n(e))}function o0(e,t,n){n===void 0&&(n=!1);const r=ro(e),o=tc(e),i=ec(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Ps(s)),[s,Ps(s)]}function i0(e){const t=Ps(e);return[Ul(e),t,Ul(t)]}function Ul(e){return e.replace(/start|end/g,t=>r0[t])}function s0(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function a0(e,t,n,r){const o=ro(e);let i=s0(Xt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(Ul)))),i}function Ps(e){return e.replace(/left|right|bottom|top/g,t=>n0[t])}function l0(e){return{top:0,right:0,bottom:0,left:0,...e}}function vm(e){return typeof e!="number"?l0(e):{top:e,right:e,bottom:e,left:e}}function js(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Id(e,t,n){let{reference:r,floating:o}=e;const i=_n(t),s=tc(t),a=ec(s),u=Xt(t),c=i==="y",f=r.x+r.width/2-o.width/2,h=r.y+r.height/2-o.height/2,d=r[a]/2-o[a]/2;let v;switch(u){case"top":v={x:f,y:r.y-o.height};break;case"bottom":v={x:f,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:h};break;case"left":v={x:r.x-o.width,y:h};break;default:v={x:r.x,y:r.y}}switch(ro(t)){case"start":v[s]-=d*(n&&c?-1:1);break;case"end":v[s]+=d*(n&&c?-1:1);break}return v}const u0=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),u=await(s.isRTL==null?void 0:s.isRTL(t));let c=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:h}=Id(c,r,u),d=r,v={},w=0;for(let y=0;y<a.length;y++){const{name:b,fn:m}=a[y],{x:p,y:g,data:k,reset:S}=await m({x:f,y:h,initialPlacement:r,placement:d,strategy:o,middlewareData:v,rects:c,platform:s,elements:{reference:e,floating:t}});f=p??f,h=g??h,v={...v,[b]:{...v[b],...k}},S&&w<=50&&(w++,typeof S=="object"&&(S.placement&&(d=S.placement),S.rects&&(c=S.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):S.rects),{x:f,y:h}=Id(c,d,u)),y=-1)}return{x:f,y:h,placement:d,strategy:o,middlewareData:v}};async function Go(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:h="floating",altBoundary:d=!1,padding:v=0}=Jt(t,e),w=vm(v),b=a[d?h==="floating"?"reference":"floating":h],m=js(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(b)))==null||n?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:f,strategy:u})),p=h==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),k=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},S=js(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:p,offsetParent:g,strategy:u}):p);return{top:(m.top-S.top+w.top)/k.y,bottom:(S.bottom-m.bottom+w.bottom)/k.y,left:(m.left-S.left+w.left)/k.x,right:(S.right-m.right+w.right)/k.x}}const c0=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:u}=t,{element:c,padding:f=0}=Jt(e,t)||{};if(c==null)return{};const h=vm(f),d={x:n,y:r},v=tc(o),w=ec(v),y=await s.getDimensions(c),b=v==="y",m=b?"top":"left",p=b?"bottom":"right",g=b?"clientHeight":"clientWidth",k=i.reference[w]+i.reference[v]-d[v]-i.floating[w],S=d[v]-i.reference[v],C=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c));let P=C?C[g]:0;(!P||!await(s.isElement==null?void 0:s.isElement(C)))&&(P=a.floating[g]||i.floating[w]);const N=k/2-S/2,I=P/2-y[w]/2-1,O=Ln(h[m],I),B=Ln(h[p],I),D=O,V=P-y[w]-B,L=P/2-y[w]/2+N,q=Wl(D,L,V),$=!u.arrow&&ro(o)!=null&&L!==q&&i.reference[w]/2-(L<D?O:B)-y[w]/2<0,Q=$?L<D?L-D:L-V:0;return{[v]:d[v]+Q,data:{[v]:q,centerOffset:L-q-Q,...$&&{alignmentOffset:Q}},reset:$}}}),d0=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:u,elements:c}=t,{mainAxis:f=!0,crossAxis:h=!0,fallbackPlacements:d,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...b}=Jt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const m=Xt(o),p=_n(a),g=Xt(a)===a,k=await(u.isRTL==null?void 0:u.isRTL(c.floating)),S=d||(g||!y?[Ps(a)]:i0(a)),C=w!=="none";!d&&C&&S.push(...a0(a,y,w,k));const P=[a,...S],N=await Go(t,b),I=[];let O=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&I.push(N[m]),h){const L=o0(o,s,k);I.push(N[L[0]],N[L[1]])}if(O=[...O,{placement:o,overflows:I}],!I.every(L=>L<=0)){var B,D;const L=(((B=i.flip)==null?void 0:B.index)||0)+1,q=P[L];if(q)return{data:{index:L,overflows:O},reset:{placement:q}};let $=(D=O.filter(Q=>Q.overflows[0]<=0).sort((Q,E)=>Q.overflows[1]-E.overflows[1])[0])==null?void 0:D.placement;if(!$)switch(v){case"bestFit":{var V;const Q=(V=O.filter(E=>{if(C){const A=_n(E.placement);return A===p||A==="y"}return!0}).map(E=>[E.placement,E.overflows.filter(A=>A>0).reduce((A,z)=>A+z,0)]).sort((E,A)=>E[1]-A[1])[0])==null?void 0:V[0];Q&&($=Q);break}case"initialPlacement":$=a;break}if(o!==$)return{reset:{placement:$}}}return{}}}};function Dd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function zd(e){return t0.some(t=>e[t]>=0)}const f0=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Jt(e,t);switch(r){case"referenceHidden":{const i=await Go(t,{...o,elementContext:"reference"}),s=Dd(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:zd(s)}}}case"escaped":{const i=await Go(t,{...o,altBoundary:!0}),s=Dd(i,n.floating);return{data:{escapedOffsets:s,escaped:zd(s)}}}default:return{}}}}};async function h0(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Xt(n),a=ro(n),u=_n(n)==="y",c=["left","top"].includes(s)?-1:1,f=i&&u?-1:1,h=Jt(t,e);let{mainAxis:d,crossAxis:v,alignmentAxis:w}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&typeof w=="number"&&(v=a==="end"?w*-1:w),u?{x:v*f,y:d*c}:{x:d*c,y:v*f}}const p0=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,u=await h0(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:s}}}}},m0=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:b=>{let{x:m,y:p}=b;return{x:m,y:p}}},...u}=Jt(e,t),c={x:n,y:r},f=await Go(t,u),h=_n(Xt(o)),d=Zu(h);let v=c[d],w=c[h];if(i){const b=d==="y"?"top":"left",m=d==="y"?"bottom":"right",p=v+f[b],g=v-f[m];v=Wl(p,v,g)}if(s){const b=h==="y"?"top":"left",m=h==="y"?"bottom":"right",p=w+f[b],g=w-f[m];w=Wl(p,w,g)}const y=a.fn({...t,[d]:v,[h]:w});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[d]:i,[h]:s}}}}}},g0=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=Jt(e,t),f={x:n,y:r},h=_n(o),d=Zu(h);let v=f[d],w=f[h];const y=Jt(a,t),b=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(u){const g=d==="y"?"height":"width",k=i.reference[d]-i.floating[g]+b.mainAxis,S=i.reference[d]+i.reference[g]-b.mainAxis;v<k?v=k:v>S&&(v=S)}if(c){var m,p;const g=d==="y"?"width":"height",k=["top","left"].includes(Xt(o)),S=i.reference[h]-i.floating[g]+(k&&((m=s.offset)==null?void 0:m[h])||0)+(k?0:b.crossAxis),C=i.reference[h]+i.reference[g]+(k?0:((p=s.offset)==null?void 0:p[h])||0)-(k?b.crossAxis:0);w<S?w=S:w>C&&(w=C)}return{[d]:v,[h]:w}}}},y0=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:a}=t,{apply:u=()=>{},...c}=Jt(e,t),f=await Go(t,c),h=Xt(o),d=ro(o),v=_n(o)==="y",{width:w,height:y}=i.floating;let b,m;h==="top"||h==="bottom"?(b=h,m=d===(await(s.isRTL==null?void 0:s.isRTL(a.floating))?"start":"end")?"left":"right"):(m=h,b=d==="end"?"top":"bottom");const p=y-f.top-f.bottom,g=w-f.left-f.right,k=Ln(y-f[b],p),S=Ln(w-f[m],g),C=!t.middlewareData.shift;let P=k,N=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(N=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(P=p),C&&!d){const O=et(f.left,0),B=et(f.right,0),D=et(f.top,0),V=et(f.bottom,0);v?N=w-2*(O!==0||B!==0?O+B:et(f.left,f.right)):P=y-2*(D!==0||V!==0?D+V:et(f.top,f.bottom))}await u({...t,availableWidth:N,availableHeight:P});const I=await s.getDimensions(a.floating);return w!==I.width||y!==I.height?{reset:{rects:!0}}:{}}}};function Js(){return typeof window<"u"}function oo(e){return xm(e)?(e.nodeName||"").toLowerCase():"#document"}function rt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ft(e){var t;return(t=(xm(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function xm(e){return Js()?e instanceof Node||e instanceof rt(e).Node:!1}function Ct(e){return Js()?e instanceof Element||e instanceof rt(e).Element:!1}function zt(e){return Js()?e instanceof HTMLElement||e instanceof rt(e).HTMLElement:!1}function Fd(e){return!Js()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof rt(e).ShadowRoot}function ui(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Pt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function v0(e){return["table","td","th"].includes(oo(e))}function Xs(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function nc(e){const t=rc(),n=Ct(e)?Pt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function x0(e){let t=In(e);for(;zt(t)&&!Jr(t);){if(nc(t))return t;if(Xs(t))return null;t=In(t)}return null}function rc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Jr(e){return["html","body","#document"].includes(oo(e))}function Pt(e){return rt(e).getComputedStyle(e)}function Zs(e){return Ct(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function In(e){if(oo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Fd(e)&&e.host||Ft(e);return Fd(t)?t.host:t}function wm(e){const t=In(e);return Jr(t)?e.ownerDocument?e.ownerDocument.body:e.body:zt(t)&&ui(t)?t:wm(t)}function Yo(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=wm(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=rt(o);if(i){const a=Vl(s);return t.concat(s,s.visualViewport||[],ui(o)?o:[],a&&n?Yo(a):[])}return t.concat(o,Yo(o,[],n))}function Vl(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function bm(e){const t=Pt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=zt(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=Cs(n)!==i||Cs(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function oc(e){return Ct(e)?e:e.contextElement}function Mr(e){const t=oc(e);if(!zt(t))return Mn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=bm(t);let s=(i?Cs(n.width):n.width)/r,a=(i?Cs(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const w0=Mn(0);function km(e){const t=rt(e);return!rc()||!t.visualViewport?w0:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function b0(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==rt(e)?!1:t}function or(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=oc(e);let s=Mn(1);t&&(r?Ct(r)&&(s=Mr(r)):s=Mr(e));const a=b0(i,n,r)?km(i):Mn(0);let u=(o.left+a.x)/s.x,c=(o.top+a.y)/s.y,f=o.width/s.x,h=o.height/s.y;if(i){const d=rt(i),v=r&&Ct(r)?rt(r):r;let w=d,y=Vl(w);for(;y&&r&&v!==w;){const b=Mr(y),m=y.getBoundingClientRect(),p=Pt(y),g=m.left+(y.clientLeft+parseFloat(p.paddingLeft))*b.x,k=m.top+(y.clientTop+parseFloat(p.paddingTop))*b.y;u*=b.x,c*=b.y,f*=b.x,h*=b.y,u+=g,c+=k,w=rt(y),y=Vl(w)}}return js({width:f,height:h,x:u,y:c})}function k0(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Ft(r),a=t?Xs(t.floating):!1;if(r===s||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=Mn(1);const f=Mn(0),h=zt(r);if((h||!h&&!i)&&((oo(r)!=="body"||ui(s))&&(u=Zs(r)),zt(r))){const d=or(r);c=Mr(r),f.x=d.x+r.clientLeft,f.y=d.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+f.x,y:n.y*c.y-u.scrollTop*c.y+f.y}}function S0(e){return Array.from(e.getClientRects())}function ql(e,t){const n=Zs(e).scrollLeft;return t?t.left+n:or(Ft(e)).left+n}function E0(e){const t=Ft(e),n=Zs(e),r=e.ownerDocument.body,o=et(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=et(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+ql(e);const a=-n.scrollTop;return Pt(r).direction==="rtl"&&(s+=et(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function C0(e,t){const n=rt(e),r=Ft(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,u=0;if(o){i=o.width,s=o.height;const c=rc();(!c||c&&t==="fixed")&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:s,x:a,y:u}}function P0(e,t){const n=or(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=zt(e)?Mr(e):Mn(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,u=o*i.x,c=r*i.y;return{width:s,height:a,x:u,y:c}}function Bd(e,t,n){let r;if(t==="viewport")r=C0(e,n);else if(t==="document")r=E0(Ft(e));else if(Ct(t))r=P0(t,n);else{const o=km(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return js(r)}function Sm(e,t){const n=In(e);return n===t||!Ct(n)||Jr(n)?!1:Pt(n).position==="fixed"||Sm(n,t)}function j0(e,t){const n=t.get(e);if(n)return n;let r=Yo(e,[],!1).filter(a=>Ct(a)&&oo(a)!=="body"),o=null;const i=Pt(e).position==="fixed";let s=i?In(e):e;for(;Ct(s)&&!Jr(s);){const a=Pt(s),u=nc(s);!u&&a.position==="fixed"&&(o=null),(i?!u&&!o:!u&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ui(s)&&!u&&Sm(e,s))?r=r.filter(f=>f!==s):o=a,s=In(s)}return t.set(e,r),r}function N0(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?Xs(t)?[]:j0(t,this._c):[].concat(n),r],a=s[0],u=s.reduce((c,f)=>{const h=Bd(t,f,o);return c.top=et(h.top,c.top),c.right=Ln(h.right,c.right),c.bottom=Ln(h.bottom,c.bottom),c.left=et(h.left,c.left),c},Bd(t,a,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function T0(e){const{width:t,height:n}=bm(e);return{width:t,height:n}}function A0(e,t,n){const r=zt(t),o=Ft(t),i=n==="fixed",s=or(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const u=Mn(0);if(r||!r&&!i)if((oo(t)!=="body"||ui(o))&&(a=Zs(t)),r){const v=or(t,!0,i,t);u.x=v.x+t.clientLeft,u.y=v.y+t.clientTop}else o&&(u.x=ql(o));let c=0,f=0;if(o&&!r&&!i){const v=o.getBoundingClientRect();f=v.top+a.scrollTop,c=v.left+a.scrollLeft-ql(o,v)}const h=s.left+a.scrollLeft-u.x-c,d=s.top+a.scrollTop-u.y-f;return{x:h,y:d,width:s.width,height:s.height}}function Ba(e){return Pt(e).position==="static"}function $d(e,t){if(!zt(e)||Pt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Ft(e)===n&&(n=n.ownerDocument.body),n}function Em(e,t){const n=rt(e);if(Xs(e))return n;if(!zt(e)){let o=In(e);for(;o&&!Jr(o);){if(Ct(o)&&!Ba(o))return o;o=In(o)}return n}let r=$d(e,t);for(;r&&v0(r)&&Ba(r);)r=$d(r,t);return r&&Jr(r)&&Ba(r)&&!nc(r)?n:r||x0(e)||n}const R0=async function(e){const t=this.getOffsetParent||Em,n=this.getDimensions,r=await n(e.floating);return{reference:A0(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function O0(e){return Pt(e).direction==="rtl"}const L0={convertOffsetParentRelativeRectToViewportRelativeRect:k0,getDocumentElement:Ft,getClippingRect:N0,getOffsetParent:Em,getElementRects:R0,getClientRects:S0,getDimensions:T0,getScale:Mr,isElement:Ct,isRTL:O0};function M0(e,t){let n=null,r;const o=Ft(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,u){a===void 0&&(a=!1),u===void 0&&(u=1),i();const{left:c,top:f,width:h,height:d}=e.getBoundingClientRect();if(a||t(),!h||!d)return;const v=Di(f),w=Di(o.clientWidth-(c+h)),y=Di(o.clientHeight-(f+d)),b=Di(c),p={rootMargin:-v+"px "+-w+"px "+-y+"px "+-b+"px",threshold:et(0,Ln(1,u))||1};let g=!0;function k(S){const C=S[0].intersectionRatio;if(C!==u){if(!g)return s();C?s(!1,C):r=setTimeout(()=>{s(!1,1e-7)},1e3)}g=!1}try{n=new IntersectionObserver(k,{...p,root:o.ownerDocument})}catch{n=new IntersectionObserver(k,p)}n.observe(e)}return s(!0),i}function _0(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,c=oc(e),f=o||i?[...c?Yo(c):[],...Yo(t)]:[];f.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),i&&m.addEventListener("resize",n)});const h=c&&a?M0(c,n):null;let d=-1,v=null;s&&(v=new ResizeObserver(m=>{let[p]=m;p&&p.target===c&&v&&(v.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var g;(g=v)==null||g.observe(t)})),n()}),c&&!u&&v.observe(c),v.observe(t));let w,y=u?or(e):null;u&&b();function b(){const m=or(e);y&&(m.x!==y.x||m.y!==y.y||m.width!==y.width||m.height!==y.height)&&n(),y=m,w=requestAnimationFrame(b)}return n(),()=>{var m;f.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),h==null||h(),(m=v)==null||m.disconnect(),v=null,u&&cancelAnimationFrame(w)}}const I0=p0,D0=m0,z0=d0,F0=y0,B0=f0,Hd=c0,$0=g0,H0=(e,t,n)=>{const r=new Map,o={platform:L0,...n},i={...o.platform,_c:r};return u0(e,t,{...o,platform:i})};var Zi=typeof document<"u"?x.useLayoutEffect:x.useEffect;function Ns(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ns(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Ns(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Cm(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Wd(e,t){const n=Cm(e);return Math.round(t*n)/n}function $a(e){const t=x.useRef(e);return Zi(()=>{t.current=e}),t}function W0(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:u,open:c}=e,[f,h]=x.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,v]=x.useState(r);Ns(d,r)||v(r);const[w,y]=x.useState(null),[b,m]=x.useState(null),p=x.useCallback(E=>{E!==C.current&&(C.current=E,y(E))},[]),g=x.useCallback(E=>{E!==P.current&&(P.current=E,m(E))},[]),k=i||w,S=s||b,C=x.useRef(null),P=x.useRef(null),N=x.useRef(f),I=u!=null,O=$a(u),B=$a(o),D=$a(c),V=x.useCallback(()=>{if(!C.current||!P.current)return;const E={placement:t,strategy:n,middleware:d};B.current&&(E.platform=B.current),H0(C.current,P.current,E).then(A=>{const z={...A,isPositioned:D.current!==!1};L.current&&!Ns(N.current,z)&&(N.current=z,li.flushSync(()=>{h(z)}))})},[d,t,n,B,D]);Zi(()=>{c===!1&&N.current.isPositioned&&(N.current.isPositioned=!1,h(E=>({...E,isPositioned:!1})))},[c]);const L=x.useRef(!1);Zi(()=>(L.current=!0,()=>{L.current=!1}),[]),Zi(()=>{if(k&&(C.current=k),S&&(P.current=S),k&&S){if(O.current)return O.current(k,S,V);V()}},[k,S,V,O,I]);const q=x.useMemo(()=>({reference:C,floating:P,setReference:p,setFloating:g}),[p,g]),$=x.useMemo(()=>({reference:k,floating:S}),[k,S]),Q=x.useMemo(()=>{const E={position:n,left:0,top:0};if(!$.floating)return E;const A=Wd($.floating,f.x),z=Wd($.floating,f.y);return a?{...E,transform:"translate("+A+"px, "+z+"px)",...Cm($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:z}},[n,a,$.floating,f.x,f.y]);return x.useMemo(()=>({...f,update:V,refs:q,elements:$,floatingStyles:Q}),[f,V,q,$,Q])}const U0=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Hd({element:r.current,padding:o}).fn(n):{}:r?Hd({element:r,padding:o}).fn(n):{}}}},V0=(e,t)=>({...I0(e),options:[e,t]}),q0=(e,t)=>({...D0(e),options:[e,t]}),Q0=(e,t)=>({...$0(e),options:[e,t]}),K0=(e,t)=>({...z0(e),options:[e,t]}),G0=(e,t)=>({...F0(e),options:[e,t]}),Y0=(e,t)=>({...B0(e),options:[e,t]}),J0=(e,t)=>({...U0(e),options:[e,t]});var X0="Arrow",Pm=x.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return l.jsx(Ge.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});Pm.displayName=X0;var Z0=Pm;function e1(e,t=[]){let n=[];function r(i,s){const a=x.createContext(s),u=n.length;n=[...n,s];function c(h){const{scope:d,children:v,...w}=h,y=(d==null?void 0:d[e][u])||a,b=x.useMemo(()=>w,Object.values(w));return l.jsx(y.Provider,{value:b,children:v})}function f(h,d){const v=(d==null?void 0:d[e][u])||a,w=x.useContext(v);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,f]}const o=()=>{const i=n.map(s=>x.createContext(s));return function(a){const u=(a==null?void 0:a[e])||i;return x.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return o.scopeName=e,[r,t1(o,...t)]}function t1(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((a,{useScope:u,scopeName:c})=>{const h=u(i)[`__scope${c}`];return{...a,...h}},{});return x.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function n1(e){const[t,n]=x.useState(void 0);return rr(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,a;if("borderBoxSize"in i){const u=i.borderBoxSize,c=Array.isArray(u)?u[0]:u;s=c.inlineSize,a=c.blockSize}else s=e.offsetWidth,a=e.offsetHeight;n({width:s,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var jm="Popper",[Nm,Tm]=e1(jm),[Wk,Am]=Nm(jm),Rm="PopperAnchor",Om=x.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Am(Rm,n),s=x.useRef(null),a=Et(t,s);return x.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:l.jsx(Ge.div,{...o,ref:a})});Om.displayName=Rm;var ic="PopperContent",[r1,o1]=Nm(ic),Lm=x.forwardRef((e,t)=>{var pt,en,tn,nn,fi,cr;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:h="partial",hideWhenDetached:d=!1,updatePositionStrategy:v="optimized",onPlaced:w,...y}=e,b=Am(ic,n),[m,p]=x.useState(null),g=Et(t,Bn=>p(Bn)),[k,S]=x.useState(null),C=n1(k),P=(C==null?void 0:C.width)??0,N=(C==null?void 0:C.height)??0,I=r+(i!=="center"?"-"+i:""),O=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},B=Array.isArray(c)?c:[c],D=B.length>0,V={padding:O,boundary:B.filter(s1),altBoundary:D},{refs:L,floatingStyles:q,placement:$,isPositioned:Q,middlewareData:E}=W0({strategy:"fixed",placement:I,whileElementsMounted:(...Bn)=>_0(...Bn,{animationFrame:v==="always"}),elements:{reference:b.anchor},middleware:[V0({mainAxis:o+N,alignmentAxis:s}),u&&q0({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?Q0():void 0,...V}),u&&K0({...V}),G0({...V,apply:({elements:Bn,rects:so,availableWidth:hi,availableHeight:rn})=>{const{width:ia,height:sa}=so.reference,je=Bn.floating.style;je.setProperty("--radix-popper-available-width",`${hi}px`),je.setProperty("--radix-popper-available-height",`${rn}px`),je.setProperty("--radix-popper-anchor-width",`${ia}px`),je.setProperty("--radix-popper-anchor-height",`${sa}px`)}}),k&&J0({element:k,padding:a}),a1({arrowWidth:P,arrowHeight:N}),d&&Y0({strategy:"referenceHidden",...V})]}),[A,z]=Im($),_=Dt(w);rr(()=>{Q&&(_==null||_())},[Q,_]);const F=(pt=E.arrow)==null?void 0:pt.x,Y=(en=E.arrow)==null?void 0:en.y,he=((tn=E.arrow)==null?void 0:tn.centerOffset)!==0,[Ye,oe]=x.useState();return rr(()=>{m&&oe(window.getComputedStyle(m).zIndex)},[m]),l.jsx("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:Q?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ye,"--radix-popper-transform-origin":[(nn=E.transformOrigin)==null?void 0:nn.x,(fi=E.transformOrigin)==null?void 0:fi.y].join(" "),...((cr=E.hide)==null?void 0:cr.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:l.jsx(r1,{scope:n,placedSide:A,onArrowChange:S,arrowX:F,arrowY:Y,shouldHideArrow:he,children:l.jsx(Ge.div,{"data-side":A,"data-align":z,...y,ref:g,style:{...y.style,animation:Q?void 0:"none"}})})})});Lm.displayName=ic;var Mm="PopperArrow",i1={top:"bottom",right:"left",bottom:"top",left:"right"},_m=x.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=o1(Mm,r),s=i1[i.placedSide];return l.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:l.jsx(Z0,{...o,ref:n,style:{...o.style,display:"block"}})})});_m.displayName=Mm;function s1(e){return e!==null}var a1=e=>({name:"transformOrigin",options:e,fn(t){var b,m,p;const{placement:n,rects:r,middlewareData:o}=t,s=((b=o.arrow)==null?void 0:b.centerOffset)!==0,a=s?0:e.arrowWidth,u=s?0:e.arrowHeight,[c,f]=Im(n),h={start:"0%",center:"50%",end:"100%"}[f],d=(((m=o.arrow)==null?void 0:m.x)??0)+a/2,v=(((p=o.arrow)==null?void 0:p.y)??0)+u/2;let w="",y="";return c==="bottom"?(w=s?h:`${d}px`,y=`${-u}px`):c==="top"?(w=s?h:`${d}px`,y=`${r.floating.height+u}px`):c==="right"?(w=`${-u}px`,y=s?h:`${v}px`):c==="left"&&(w=`${r.floating.width+u}px`,y=s?h:`${v}px`),{data:{x:w,y}}}});function Im(e){const[t,n="center"]=e.split("-");return[t,n]}var l1=Om,u1=Lm,c1=_m,[ea,Uk]=Op("Tooltip",[Tm]),sc=Tm(),Dm="TooltipProvider",d1=700,Ud="tooltip.open",[f1,zm]=ea(Dm),Fm=e=>{const{__scopeTooltip:t,delayDuration:n=d1,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,a]=x.useState(!0),u=x.useRef(!1),c=x.useRef(0);return x.useEffect(()=>{const f=c.current;return()=>window.clearTimeout(f)},[]),l.jsx(f1,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:x.useCallback(()=>{window.clearTimeout(c.current),a(!1)},[]),onClose:x.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>a(!0),r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:x.useCallback(f=>{u.current=f},[]),disableHoverableContent:o,children:i})};Fm.displayName=Dm;var Bm="Tooltip",[Vk,ta]=ea(Bm),Ql="TooltipTrigger",h1=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=ta(Ql,n),i=zm(Ql,n),s=sc(n),a=x.useRef(null),u=Et(t,a,o.onTriggerChange),c=x.useRef(!1),f=x.useRef(!1),h=x.useCallback(()=>c.current=!1,[]);return x.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),l.jsx(l1,{asChild:!0,...s,children:l.jsx(Ge.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:u,onPointerMove:Se(e.onPointerMove,d=>{d.pointerType!=="touch"&&!f.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:Se(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:Se(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:Se(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:Se(e.onBlur,o.onClose),onClick:Se(e.onClick,o.onClose)})})});h1.displayName=Ql;var p1="TooltipPortal",[qk,m1]=ea(p1,{forceMount:void 0}),Xr="TooltipContent",$m=x.forwardRef((e,t)=>{const n=m1(Xr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=ta(Xr,e.__scopeTooltip);return l.jsx(Qu,{present:r||s.open,children:s.disableHoverableContent?l.jsx(Hm,{side:o,...i,ref:t}):l.jsx(g1,{side:o,...i,ref:t})})}),g1=x.forwardRef((e,t)=>{const n=ta(Xr,e.__scopeTooltip),r=zm(Xr,e.__scopeTooltip),o=x.useRef(null),i=Et(t,o),[s,a]=x.useState(null),{trigger:u,onClose:c}=n,f=o.current,{onPointerInTransitChange:h}=r,d=x.useCallback(()=>{a(null),h(!1)},[h]),v=x.useCallback((w,y)=>{const b=w.currentTarget,m={x:w.clientX,y:w.clientY},p=w1(m,b.getBoundingClientRect()),g=b1(m,p),k=k1(y.getBoundingClientRect()),S=E1([...g,...k]);a(S),h(!0)},[h]);return x.useEffect(()=>()=>d(),[d]),x.useEffect(()=>{if(u&&f){const w=b=>v(b,f),y=b=>v(b,u);return u.addEventListener("pointerleave",w),f.addEventListener("pointerleave",y),()=>{u.removeEventListener("pointerleave",w),f.removeEventListener("pointerleave",y)}}},[u,f,v,d]),x.useEffect(()=>{if(s){const w=y=>{const b=y.target,m={x:y.clientX,y:y.clientY},p=(u==null?void 0:u.contains(b))||(f==null?void 0:f.contains(b)),g=!S1(m,s);p?d():g&&(d(),c())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[u,f,s,c,d]),l.jsx(Hm,{...e,ref:i})}),[y1,v1]=ea(Bm,{isInside:!1}),Hm=x.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...a}=e,u=ta(Xr,n),c=sc(n),{onClose:f}=u;return x.useEffect(()=>(document.addEventListener(Ud,f),()=>document.removeEventListener(Ud,f)),[f]),x.useEffect(()=>{if(u.trigger){const h=d=>{const v=d.target;v!=null&&v.contains(u.trigger)&&f()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[u.trigger,f]),l.jsx(qu,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:h=>h.preventDefault(),onDismiss:f,children:l.jsxs(u1,{"data-state":u.stateAttribute,...c,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(Rp,{children:r}),l.jsx(y1,{scope:n,isInside:!0,children:l.jsx(wx,{id:u.contentId,role:"tooltip",children:o||r})})]})})});$m.displayName=Xr;var Wm="TooltipArrow",x1=x.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=sc(n);return v1(Wm,n).isInside?null:l.jsx(c1,{...o,...r,ref:t})});x1.displayName=Wm;function w1(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function b1(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function k1(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function S1(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const a=t[i].x,u=t[i].y,c=t[s].x,f=t[s].y;u>r!=f>r&&n<(c-a)*(r-u)/(f-u)+a&&(o=!o)}return o}function E1(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),C1(t)}function C1(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var P1=Fm,Um=$m;const j1=P1,N1=x.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(Um,{ref:r,sideOffset:t,className:ar("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));N1.displayName=Um.displayName;var na=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},ra=typeof window>"u"||"Deno"in globalThis;function yt(){}function T1(e,t){return typeof e=="function"?e(t):e}function A1(e){return typeof e=="number"&&e>=0&&e!==1/0}function R1(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Vd(e,t){return typeof e=="function"?e(t):e}function O1(e,t){return typeof e=="function"?e(t):e}function qd(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==ac(s,t.options))return!1}else if(!Xo(t.queryKey,s))return!1}if(n!=="all"){const u=t.isActive();if(n==="active"&&!u||n==="inactive"&&u)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||o&&o!==t.state.fetchStatus||i&&!i(t))}function Qd(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(Jo(t.options.mutationKey)!==Jo(i))return!1}else if(!Xo(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function ac(e,t){return((t==null?void 0:t.queryKeyHashFn)||Jo)(e)}function Jo(e){return JSON.stringify(e,(t,n)=>Kl(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Xo(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Xo(e[n],t[n])):!1}function Vm(e,t){if(e===t)return e;const n=Kd(e)&&Kd(t);if(n||Kl(e)&&Kl(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,a=n?[]:{};let u=0;for(let c=0;c<s;c++){const f=n?c:i[c];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,u++):(a[f]=Vm(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&u++)}return o===s&&u===o?e:a}return t}function Kd(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Kl(e){if(!Gd(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Gd(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Gd(e){return Object.prototype.toString.call(e)==="[object Object]"}function L1(e){return new Promise(t=>{setTimeout(t,e)})}function M1(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Vm(e,t):t}function _1(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function I1(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var lc=Symbol();function qm(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===lc?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var qn,pn,_r,af,D1=(af=class extends na{constructor(){super();Z(this,qn);Z(this,pn);Z(this,_r);U(this,_r,t=>{if(!ra&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){j(this,pn)||this.setEventListener(j(this,_r))}onUnsubscribe(){var t;this.hasListeners()||((t=j(this,pn))==null||t.call(this),U(this,pn,void 0))}setEventListener(t){var n;U(this,_r,t),(n=j(this,pn))==null||n.call(this),U(this,pn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){j(this,qn)!==t&&(U(this,qn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof j(this,qn)=="boolean"?j(this,qn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},qn=new WeakMap,pn=new WeakMap,_r=new WeakMap,af),Qm=new D1,Ir,mn,Dr,lf,z1=(lf=class extends na{constructor(){super();Z(this,Ir,!0);Z(this,mn);Z(this,Dr);U(this,Dr,t=>{if(!ra&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){j(this,mn)||this.setEventListener(j(this,Dr))}onUnsubscribe(){var t;this.hasListeners()||((t=j(this,mn))==null||t.call(this),U(this,mn,void 0))}setEventListener(t){var n;U(this,Dr,t),(n=j(this,mn))==null||n.call(this),U(this,mn,t(this.setOnline.bind(this)))}setOnline(t){j(this,Ir)!==t&&(U(this,Ir,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return j(this,Ir)}},Ir=new WeakMap,mn=new WeakMap,Dr=new WeakMap,lf),Ts=new z1;function F1(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function B1(e){return Math.min(1e3*2**e,3e4)}function Km(e){return(e??"online")==="online"?Ts.isOnline():!0}var Gm=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Ha(e){return e instanceof Gm}function Ym(e){let t=!1,n=0,r=!1,o;const i=F1(),s=y=>{var b;r||(d(new Gm(y)),(b=e.abort)==null||b.call(e))},a=()=>{t=!0},u=()=>{t=!1},c=()=>Qm.isFocused()&&(e.networkMode==="always"||Ts.isOnline())&&e.canRun(),f=()=>Km(e.networkMode)&&e.canRun(),h=y=>{var b;r||(r=!0,(b=e.onSuccess)==null||b.call(e,y),o==null||o(),i.resolve(y))},d=y=>{var b;r||(r=!0,(b=e.onError)==null||b.call(e,y),o==null||o(),i.reject(y))},v=()=>new Promise(y=>{var b;o=m=>{(r||c())&&y(m)},(b=e.onPause)==null||b.call(e)}).then(()=>{var y;o=void 0,r||(y=e.onContinue)==null||y.call(e)}),w=()=>{if(r)return;let y;const b=n===0?e.initialPromise:void 0;try{y=b??e.fn()}catch(m){y=Promise.reject(m)}Promise.resolve(y).then(h).catch(m=>{var C;if(r)return;const p=e.retry??(ra?0:3),g=e.retryDelay??B1,k=typeof g=="function"?g(n,m):g,S=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,m);if(t||!S){d(m);return}n++,(C=e.onFail)==null||C.call(e,n,m),L1(k).then(()=>c()?void 0:v()).then(()=>{t?d(m):w()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:a,continueRetry:u,canStart:f,start:()=>(f()?w():v().then(w),i)}}function $1(){let e=[],t=0,n=a=>{a()},r=a=>{a()},o=a=>setTimeout(a,0);const i=a=>{t?e.push(a):o(()=>{n(a)})},s=()=>{const a=e;e=[],a.length&&o(()=>{r(()=>{a.forEach(u=>{n(u)})})})};return{batch:a=>{let u;t++;try{u=a()}finally{t--,t||s()}return u},batchCalls:a=>(...u)=>{i(()=>{a(...u)})},schedule:i,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{o=a}}}var Be=$1(),Qn,uf,Jm=(uf=class{constructor(){Z(this,Qn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),A1(this.gcTime)&&U(this,Qn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(ra?1/0:5*60*1e3))}clearGcTimeout(){j(this,Qn)&&(clearTimeout(j(this,Qn)),U(this,Qn,void 0))}},Qn=new WeakMap,uf),zr,Fr,at,Me,ti,Kn,vt,$t,cf,H1=(cf=class extends Jm{constructor(t){super();Z(this,vt);Z(this,zr);Z(this,Fr);Z(this,at);Z(this,Me);Z(this,ti);Z(this,Kn);U(this,Kn,!1),U(this,ti,t.defaultOptions),this.setOptions(t.options),this.observers=[],U(this,at,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,U(this,zr,U1(this.options)),this.state=t.state??j(this,zr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=j(this,Me))==null?void 0:t.promise}setOptions(t){this.options={...j(this,ti),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&j(this,at).remove(this)}setData(t,n){const r=M1(this.state.data,t,this.options);return Re(this,vt,$t).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Re(this,vt,$t).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=j(this,Me))==null?void 0:r.promise;return(o=j(this,Me))==null||o.cancel(t),n?n.then(yt).catch(yt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(j(this,zr))}isActive(){return this.observers.some(t=>O1(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===lc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!R1(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=j(this,Me))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=j(this,Me))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),j(this,at).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(j(this,Me)&&(j(this,Kn)?j(this,Me).cancel({revert:!0}):j(this,Me).cancelRetry()),this.scheduleGc()),j(this,at).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Re(this,vt,$t).call(this,{type:"invalidate"})}fetch(t,n){var u,c,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(j(this,Me))return j(this,Me).continueRetry(),j(this,Me).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(d=>d.options.queryFn);h&&this.setOptions(h.options)}const r=new AbortController,o=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(U(this,Kn,!0),r.signal)})},i=()=>{const h=qm(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return o(d),U(this,Kn,!1),this.options.persister?this.options.persister(h,d,this):h(d)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(u=this.options.behavior)==null||u.onFetch(s,this),U(this,Fr,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=s.fetchOptions)==null?void 0:c.meta))&&Re(this,vt,$t).call(this,{type:"fetch",meta:(f=s.fetchOptions)==null?void 0:f.meta});const a=h=>{var d,v,w,y;Ha(h)&&h.silent||Re(this,vt,$t).call(this,{type:"error",error:h}),Ha(h)||((v=(d=j(this,at).config).onError)==null||v.call(d,h,this),(y=(w=j(this,at).config).onSettled)==null||y.call(w,this.state.data,h,this)),this.scheduleGc()};return U(this,Me,Ym({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:h=>{var d,v,w,y;if(h===void 0){a(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(b){a(b);return}(v=(d=j(this,at).config).onSuccess)==null||v.call(d,h,this),(y=(w=j(this,at).config).onSettled)==null||y.call(w,h,this.state.error,this),this.scheduleGc()},onError:a,onFail:(h,d)=>{Re(this,vt,$t).call(this,{type:"failed",failureCount:h,error:d})},onPause:()=>{Re(this,vt,$t).call(this,{type:"pause"})},onContinue:()=>{Re(this,vt,$t).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),j(this,Me).start()}},zr=new WeakMap,Fr=new WeakMap,at=new WeakMap,Me=new WeakMap,ti=new WeakMap,Kn=new WeakMap,vt=new WeakSet,$t=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...W1(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Ha(o)&&o.revert&&j(this,Fr)?{...j(this,Fr),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Be.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),j(this,at).notify({query:this,type:"updated",action:t})})},cf);function W1(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Km(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function U1(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Rt,df,V1=(df=class extends na{constructor(t={}){super();Z(this,Rt);this.config=t,U(this,Rt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??ac(o,n);let s=this.get(i);return s||(s=new H1({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){j(this,Rt).has(t.queryHash)||(j(this,Rt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=j(this,Rt).get(t.queryHash);n&&(t.destroy(),n===t&&j(this,Rt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return j(this,Rt).get(t)}getAll(){return[...j(this,Rt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>qd(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>qd(t,r)):n}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Be.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Be.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Rt=new WeakMap,df),Ot,De,Gn,Lt,un,ff,q1=(ff=class extends Jm{constructor(t){super();Z(this,Lt);Z(this,Ot);Z(this,De);Z(this,Gn);this.mutationId=t.mutationId,U(this,De,t.mutationCache),U(this,Ot,[]),this.state=t.state||Q1(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){j(this,Ot).includes(t)||(j(this,Ot).push(t),this.clearGcTimeout(),j(this,De).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){U(this,Ot,j(this,Ot).filter(n=>n!==t)),this.scheduleGc(),j(this,De).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){j(this,Ot).length||(this.state.status==="pending"?this.scheduleGc():j(this,De).remove(this))}continue(){var t;return((t=j(this,Gn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,a,u,c,f,h,d,v,w,y,b,m,p,g,k,S,C,P;U(this,Gn,Ym({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(N,I)=>{Re(this,Lt,un).call(this,{type:"failed",failureCount:N,error:I})},onPause:()=>{Re(this,Lt,un).call(this,{type:"pause"})},onContinue:()=>{Re(this,Lt,un).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>j(this,De).canRun(this)}));const n=this.state.status==="pending",r=!j(this,Gn).canStart();try{if(!n){Re(this,Lt,un).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=j(this,De).config).onMutate)==null?void 0:i.call(o,t,this));const I=await((a=(s=this.options).onMutate)==null?void 0:a.call(s,t));I!==this.state.context&&Re(this,Lt,un).call(this,{type:"pending",context:I,variables:t,isPaused:r})}const N=await j(this,Gn).start();return await((c=(u=j(this,De).config).onSuccess)==null?void 0:c.call(u,N,t,this.state.context,this)),await((h=(f=this.options).onSuccess)==null?void 0:h.call(f,N,t,this.state.context)),await((v=(d=j(this,De).config).onSettled)==null?void 0:v.call(d,N,null,this.state.variables,this.state.context,this)),await((y=(w=this.options).onSettled)==null?void 0:y.call(w,N,null,t,this.state.context)),Re(this,Lt,un).call(this,{type:"success",data:N}),N}catch(N){try{throw await((m=(b=j(this,De).config).onError)==null?void 0:m.call(b,N,t,this.state.context,this)),await((g=(p=this.options).onError)==null?void 0:g.call(p,N,t,this.state.context)),await((S=(k=j(this,De).config).onSettled)==null?void 0:S.call(k,void 0,N,this.state.variables,this.state.context,this)),await((P=(C=this.options).onSettled)==null?void 0:P.call(C,void 0,N,t,this.state.context)),N}finally{Re(this,Lt,un).call(this,{type:"error",error:N})}}finally{j(this,De).runNext(this)}}},Ot=new WeakMap,De=new WeakMap,Gn=new WeakMap,Lt=new WeakSet,un=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Be.batch(()=>{j(this,Ot).forEach(r=>{r.onMutationUpdate(t)}),j(this,De).notify({mutation:this,type:"updated",action:t})})},ff);function Q1(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Je,ni,hf,K1=(hf=class extends na{constructor(t={}){super();Z(this,Je);Z(this,ni);this.config=t,U(this,Je,new Map),U(this,ni,Date.now())}build(t,n,r){const o=new q1({mutationCache:this,mutationId:++gi(this,ni)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=zi(t),r=j(this,Je).get(n)??[];r.push(t),j(this,Je).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=zi(t);if(j(this,Je).has(n)){const o=(r=j(this,Je).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?j(this,Je).delete(n):j(this,Je).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=j(this,Je).get(zi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=j(this,Je).get(zi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...j(this,Je).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Qd(n,r))}findAll(t={}){return this.getAll().filter(n=>Qd(t,n))}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Be.batch(()=>Promise.all(t.map(n=>n.continue().catch(yt))))}},Je=new WeakMap,ni=new WeakMap,hf);function zi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Yd(e){return{onFetch:(t,n)=>{var f,h,d,v,w;const r=t.options,o=(d=(h=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:h.fetchMore)==null?void 0:d.direction,i=((v=t.state.data)==null?void 0:v.pages)||[],s=((w=t.state.data)==null?void 0:w.pageParams)||[];let a={pages:[],pageParams:[]},u=0;const c=async()=>{let y=!1;const b=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},m=qm(t.options,t.fetchOptions),p=async(g,k,S)=>{if(y)return Promise.reject();if(k==null&&g.pages.length)return Promise.resolve(g);const C={queryKey:t.queryKey,pageParam:k,direction:S?"backward":"forward",meta:t.options.meta};b(C);const P=await m(C),{maxPages:N}=t.options,I=S?I1:_1;return{pages:I(g.pages,P,N),pageParams:I(g.pageParams,k,N)}};if(o&&i.length){const g=o==="backward",k=g?G1:Jd,S={pages:i,pageParams:s},C=k(r,S);a=await p(S,C,g)}else{const g=e??i.length;do{const k=u===0?s[0]??r.initialPageParam:Jd(r,a);if(u>0&&k==null)break;a=await p(a,k),u++}while(u<g)}return a};t.options.persister?t.fetchFn=()=>{var y,b;return(b=(y=t.options).persister)==null?void 0:b.call(y,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function Jd(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function G1(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ye,gn,yn,Br,$r,vn,Hr,Wr,pf,Y1=(pf=class{constructor(e={}){Z(this,ye);Z(this,gn);Z(this,yn);Z(this,Br);Z(this,$r);Z(this,vn);Z(this,Hr);Z(this,Wr);U(this,ye,e.queryCache||new V1),U(this,gn,e.mutationCache||new K1),U(this,yn,e.defaultOptions||{}),U(this,Br,new Map),U(this,$r,new Map),U(this,vn,0)}mount(){gi(this,vn)._++,j(this,vn)===1&&(U(this,Hr,Qm.subscribe(async e=>{e&&(await this.resumePausedMutations(),j(this,ye).onFocus())})),U(this,Wr,Ts.subscribe(async e=>{e&&(await this.resumePausedMutations(),j(this,ye).onOnline())})))}unmount(){var e,t;gi(this,vn)._--,j(this,vn)===0&&((e=j(this,Hr))==null||e.call(this),U(this,Hr,void 0),(t=j(this,Wr))==null||t.call(this),U(this,Wr,void 0))}isFetching(e){return j(this,ye).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return j(this,gn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=j(this,ye).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=j(this,ye).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Vd(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return j(this,ye).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=j(this,ye).get(r.queryHash),i=o==null?void 0:o.state.data,s=T1(t,i);if(s!==void 0)return j(this,ye).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Be.batch(()=>j(this,ye).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=j(this,ye).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=j(this,ye);Be.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=j(this,ye),r={type:"active",...e};return Be.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Be.batch(()=>j(this,ye).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(yt).catch(yt)}invalidateQueries(e={},t={}){return Be.batch(()=>{if(j(this,ye).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Be.batch(()=>j(this,ye).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(yt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(yt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=j(this,ye).build(this,t);return n.isStaleByTime(Vd(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(yt).catch(yt)}fetchInfiniteQuery(e){return e.behavior=Yd(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(yt).catch(yt)}ensureInfiniteQueryData(e){return e.behavior=Yd(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ts.isOnline()?j(this,gn).resumePausedMutations():Promise.resolve()}getQueryCache(){return j(this,ye)}getMutationCache(){return j(this,gn)}getDefaultOptions(){return j(this,yn)}setDefaultOptions(e){U(this,yn,e)}setQueryDefaults(e,t){j(this,Br).set(Jo(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...j(this,Br).values()];let n={};return t.forEach(r=>{Xo(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){j(this,$r).set(Jo(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...j(this,$r).values()];let n={};return t.forEach(r=>{Xo(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...j(this,yn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ac(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===lc&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...j(this,yn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){j(this,ye).clear(),j(this,gn).clear()}},ye=new WeakMap,gn=new WeakMap,yn=new WeakMap,Br=new WeakMap,$r=new WeakMap,vn=new WeakMap,Hr=new WeakMap,Wr=new WeakMap,pf),J1=x.createContext(void 0),X1=({client:e,children:t})=>(x.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),l.jsx(J1.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Zo(){return Zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zo.apply(this,arguments)}var bn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(bn||(bn={}));const Xd="popstate";function Z1(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:a}=r.location;return Gl("",{pathname:i,search:s,hash:a},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:As(o)}return tb(t,n,null,e)}function be(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Xm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function eb(){return Math.random().toString(36).substr(2,8)}function Zd(e,t){return{usr:e.state,key:e.key,idx:t}}function Gl(e,t,n,r){return n===void 0&&(n=null),Zo({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?io(t):t,{state:n,key:t&&t.key||r||eb()})}function As(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function io(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function tb(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,a=bn.Pop,u=null,c=f();c==null&&(c=0,s.replaceState(Zo({},s.state,{idx:c}),""));function f(){return(s.state||{idx:null}).idx}function h(){a=bn.Pop;let b=f(),m=b==null?null:b-c;c=b,u&&u({action:a,location:y.location,delta:m})}function d(b,m){a=bn.Push;let p=Gl(y.location,b,m);c=f()+1;let g=Zd(p,c),k=y.createHref(p);try{s.pushState(g,"",k)}catch(S){if(S instanceof DOMException&&S.name==="DataCloneError")throw S;o.location.assign(k)}i&&u&&u({action:a,location:y.location,delta:1})}function v(b,m){a=bn.Replace;let p=Gl(y.location,b,m);c=f();let g=Zd(p,c),k=y.createHref(p);s.replaceState(g,"",k),i&&u&&u({action:a,location:y.location,delta:0})}function w(b){let m=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof b=="string"?b:As(b);return p=p.replace(/ $/,"%20"),be(m,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,m)}let y={get action(){return a},get location(){return e(o,s)},listen(b){if(u)throw new Error("A history only accepts one active listener");return o.addEventListener(Xd,h),u=b,()=>{o.removeEventListener(Xd,h),u=null}},createHref(b){return t(o,b)},createURL:w,encodeLocation(b){let m=w(b);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:d,replace:v,go(b){return s.go(b)}};return y}var ef;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ef||(ef={}));function nb(e,t,n){return n===void 0&&(n="/"),rb(e,t,n,!1)}function rb(e,t,n,r){let o=typeof t=="string"?io(t):t,i=uc(o.pathname||"/",n);if(i==null)return null;let s=Zm(e);ob(s);let a=null;for(let u=0;a==null&&u<s.length;++u){let c=mb(i);a=hb(s[u],c,r)}return a}function Zm(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,a)=>{let u={relativePath:a===void 0?i.path||"":a,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};u.relativePath.startsWith("/")&&(be(u.relativePath.startsWith(r),'Absolute route path "'+u.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),u.relativePath=u.relativePath.slice(r.length));let c=An([r,u.relativePath]),f=n.concat(u);i.children&&i.children.length>0&&(be(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+c+'".')),Zm(i.children,t,f,c)),!(i.path==null&&!i.index)&&t.push({path:c,score:db(c,i.index),routesMeta:f})};return e.forEach((i,s)=>{var a;if(i.path===""||!((a=i.path)!=null&&a.includes("?")))o(i,s);else for(let u of eg(i.path))o(i,s,u)}),t}function eg(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=eg(r.join("/")),a=[];return a.push(...s.map(u=>u===""?i:[i,u].join("/"))),o&&a.push(...s),a.map(u=>e.startsWith("/")&&u===""?"/":u)}function ob(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:fb(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ib=/^:[\w-]+$/,sb=3,ab=2,lb=1,ub=10,cb=-2,tf=e=>e==="*";function db(e,t){let n=e.split("/"),r=n.length;return n.some(tf)&&(r+=cb),t&&(r+=ab),n.filter(o=>!tf(o)).reduce((o,i)=>o+(ib.test(i)?sb:i===""?lb:ub),r)}function fb(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function hb(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let a=0;a<r.length;++a){let u=r[a],c=a===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",h=nf({path:u.relativePath,caseSensitive:u.caseSensitive,end:c},f),d=u.route;if(!h&&c&&n&&!r[r.length-1].route.index&&(h=nf({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},f)),!h)return null;Object.assign(o,h.params),s.push({params:o,pathname:An([i,h.pathname]),pathnameBase:xb(An([i,h.pathnameBase])),route:d}),h.pathnameBase!=="/"&&(i=An([i,h.pathnameBase]))}return s}function nf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=pb(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),a=o.slice(1);return{params:r.reduce((c,f,h)=>{let{paramName:d,isOptional:v}=f;if(d==="*"){let y=a[h]||"";s=i.slice(0,i.length-y.length).replace(/(.)\/+$/,"$1")}const w=a[h];return v&&!w?c[d]=void 0:c[d]=(w||"").replace(/%2F/g,"/"),c},{}),pathname:i,pathnameBase:s,pattern:e}}function pb(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Xm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,a,u)=>(r.push({paramName:a,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function mb(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Xm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function uc(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function gb(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?io(e):e;return{pathname:n?n.startsWith("/")?n:yb(n,t):t,search:wb(r),hash:bb(o)}}function yb(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function Wa(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function vb(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function tg(e,t){let n=vb(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function ng(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=io(e):(o=Zo({},e),be(!o.pathname||!o.pathname.includes("?"),Wa("?","pathname","search",o)),be(!o.pathname||!o.pathname.includes("#"),Wa("#","pathname","hash",o)),be(!o.search||!o.search.includes("#"),Wa("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,a;if(s==null)a=n;else{let h=t.length-1;if(!r&&s.startsWith("..")){let d=s.split("/");for(;d[0]==="..";)d.shift(),h-=1;o.pathname=d.join("/")}a=h>=0?t[h]:"/"}let u=gb(o,a),c=s&&s!=="/"&&s.endsWith("/"),f=(i||s===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(c||f)&&(u.pathname+="/"),u}const An=e=>e.join("/").replace(/\/\/+/g,"/"),xb=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),wb=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,bb=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function kb(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const rg=["post","put","patch","delete"];new Set(rg);const Sb=["get",...rg];new Set(Sb);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ei(){return ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ei.apply(this,arguments)}const cc=x.createContext(null),Eb=x.createContext(null),lr=x.createContext(null),oa=x.createContext(null),ur=x.createContext({outlet:null,matches:[],isDataRoute:!1}),og=x.createContext(null);function Cb(e,t){let{relative:n}=t===void 0?{}:t;ci()||be(!1);let{basename:r,navigator:o}=x.useContext(lr),{hash:i,pathname:s,search:a}=sg(e,{relative:n}),u=s;return r!=="/"&&(u=s==="/"?r:An([r,s])),o.createHref({pathname:u,search:a,hash:i})}function ci(){return x.useContext(oa)!=null}function di(){return ci()||be(!1),x.useContext(oa).location}function ig(e){x.useContext(lr).static||x.useLayoutEffect(e)}function Pb(){let{isDataRoute:e}=x.useContext(ur);return e?Fb():jb()}function jb(){ci()||be(!1);let e=x.useContext(cc),{basename:t,future:n,navigator:r}=x.useContext(lr),{matches:o}=x.useContext(ur),{pathname:i}=di(),s=JSON.stringify(tg(o,n.v7_relativeSplatPath)),a=x.useRef(!1);return ig(()=>{a.current=!0}),x.useCallback(function(c,f){if(f===void 0&&(f={}),!a.current)return;if(typeof c=="number"){r.go(c);return}let h=ng(c,JSON.parse(s),i,f.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:An([t,h.pathname])),(f.replace?r.replace:r.push)(h,f.state,f)},[t,r,s,i,e])}function sg(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=x.useContext(lr),{matches:o}=x.useContext(ur),{pathname:i}=di(),s=JSON.stringify(tg(o,r.v7_relativeSplatPath));return x.useMemo(()=>ng(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function Nb(e,t){return Tb(e,t)}function Tb(e,t,n,r){ci()||be(!1);let{navigator:o}=x.useContext(lr),{matches:i}=x.useContext(ur),s=i[i.length-1],a=s?s.params:{};s&&s.pathname;let u=s?s.pathnameBase:"/";s&&s.route;let c=di(),f;if(t){var h;let b=typeof t=="string"?io(t):t;u==="/"||(h=b.pathname)!=null&&h.startsWith(u)||be(!1),f=b}else f=c;let d=f.pathname||"/",v=d;if(u!=="/"){let b=u.replace(/^\//,"").split("/");v="/"+d.replace(/^\//,"").split("/").slice(b.length).join("/")}let w=nb(e,{pathname:v}),y=Mb(w&&w.map(b=>Object.assign({},b,{params:Object.assign({},a,b.params),pathname:An([u,o.encodeLocation?o.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?u:An([u,o.encodeLocation?o.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),i,n,r);return t&&y?x.createElement(oa.Provider,{value:{location:ei({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:bn.Pop}},y):y}function Ab(){let e=zb(),t=kb(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},t),n?x.createElement("pre",{style:o},n):null,null)}const Rb=x.createElement(Ab,null);class Ob extends x.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?x.createElement(ur.Provider,{value:this.props.routeContext},x.createElement(og.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Lb(e){let{routeContext:t,match:n,children:r}=e,o=x.useContext(cc);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),x.createElement(ur.Provider,{value:t},r)}function Mb(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,a=(o=n)==null?void 0:o.errors;if(a!=null){let f=s.findIndex(h=>h.route.id&&(a==null?void 0:a[h.route.id])!==void 0);f>=0||be(!1),s=s.slice(0,Math.min(s.length,f+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<s.length;f++){let h=s[f];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(c=f),h.route.id){let{loaderData:d,errors:v}=n,w=h.route.loader&&d[h.route.id]===void 0&&(!v||v[h.route.id]===void 0);if(h.route.lazy||w){u=!0,c>=0?s=s.slice(0,c+1):s=[s[0]];break}}}return s.reduceRight((f,h,d)=>{let v,w=!1,y=null,b=null;n&&(v=a&&h.route.id?a[h.route.id]:void 0,y=h.route.errorElement||Rb,u&&(c<0&&d===0?(w=!0,b=null):c===d&&(w=!0,b=h.route.hydrateFallbackElement||null)));let m=t.concat(s.slice(0,d+1)),p=()=>{let g;return v?g=y:w?g=b:h.route.Component?g=x.createElement(h.route.Component,null):h.route.element?g=h.route.element:g=f,x.createElement(Lb,{match:h,routeContext:{outlet:f,matches:m,isDataRoute:n!=null},children:g})};return n&&(h.route.ErrorBoundary||h.route.errorElement||d===0)?x.createElement(Ob,{location:n.location,revalidation:n.revalidation,component:y,error:v,children:p(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):p()},null)}var ag=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ag||{}),Rs=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Rs||{});function _b(e){let t=x.useContext(cc);return t||be(!1),t}function Ib(e){let t=x.useContext(Eb);return t||be(!1),t}function Db(e){let t=x.useContext(ur);return t||be(!1),t}function lg(e){let t=Db(),n=t.matches[t.matches.length-1];return n.route.id||be(!1),n.route.id}function zb(){var e;let t=x.useContext(og),n=Ib(Rs.UseRouteError),r=lg(Rs.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Fb(){let{router:e}=_b(ag.UseNavigateStable),t=lg(Rs.UseNavigateStable),n=x.useRef(!1);return ig(()=>{n.current=!0}),x.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,ei({fromRouteId:t},i)))},[e,t])}function K(e){be(!1)}function Bb(e){let{basename:t="/",children:n=null,location:r,navigationType:o=bn.Pop,navigator:i,static:s=!1,future:a}=e;ci()&&be(!1);let u=t.replace(/^\/*/,"/"),c=x.useMemo(()=>({basename:u,navigator:i,static:s,future:ei({v7_relativeSplatPath:!1},a)}),[u,a,i,s]);typeof r=="string"&&(r=io(r));let{pathname:f="/",search:h="",hash:d="",state:v=null,key:w="default"}=r,y=x.useMemo(()=>{let b=uc(f,u);return b==null?null:{location:{pathname:b,search:h,hash:d,state:v,key:w},navigationType:o}},[u,f,h,d,v,w,o]);return y==null?null:x.createElement(lr.Provider,{value:c},x.createElement(oa.Provider,{children:n,value:y}))}function $b(e){let{children:t,location:n}=e;return Nb(Yl(t),n)}new Promise(()=>{});function Yl(e,t){t===void 0&&(t=[]);let n=[];return x.Children.forEach(e,(r,o)=>{if(!x.isValidElement(r))return;let i=[...t,o];if(r.type===x.Fragment){n.push.apply(n,Yl(r.props.children,i));return}r.type!==K&&be(!1),!r.props.index||!r.props.children||be(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=Yl(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Jl(){return Jl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Jl.apply(this,arguments)}function Hb(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Wb(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Ub(e,t){return e.button===0&&(!t||t==="_self")&&!Wb(e)}const Vb=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],qb="6";try{window.__reactRouterVersion=qb}catch{}const Qb="startTransition",rf=Og[Qb];function Kb(e){let{basename:t,children:n,future:r,window:o}=e,i=x.useRef();i.current==null&&(i.current=Z1({window:o,v5Compat:!0}));let s=i.current,[a,u]=x.useState({action:s.action,location:s.location}),{v7_startTransition:c}=r||{},f=x.useCallback(h=>{c&&rf?rf(()=>u(h)):u(h)},[u,c]);return x.useLayoutEffect(()=>s.listen(f),[s,f]),x.createElement(Bb,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:s,future:r})}const Gb=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Yb=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ze=x.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:a,target:u,to:c,preventScrollReset:f,viewTransition:h}=t,d=Hb(t,Vb),{basename:v}=x.useContext(lr),w,y=!1;if(typeof c=="string"&&Yb.test(c)&&(w=c,Gb))try{let g=new URL(window.location.href),k=c.startsWith("//")?new URL(g.protocol+c):new URL(c),S=uc(k.pathname,v);k.origin===g.origin&&S!=null?c=S+k.search+k.hash:y=!0}catch{}let b=Cb(c,{relative:o}),m=Jb(c,{replace:s,state:a,target:u,preventScrollReset:f,relative:o,viewTransition:h});function p(g){r&&r(g),g.defaultPrevented||m(g)}return x.createElement("a",Jl({},d,{href:w||b,onClick:y||i?r:p,ref:n,target:u}))});var of;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(of||(of={}));var sf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(sf||(sf={}));function Jb(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:a}=t===void 0?{}:t,u=Pb(),c=di(),f=sg(e,{relative:s});return x.useCallback(h=>{if(Ub(h,n)){h.preventDefault();let d=r!==void 0?r:As(c)===As(f);u(e,{replace:d,state:o,preventScrollReset:i,relative:s,viewTransition:a})}},[c,u,f,r,o,n,e,i,s,a])}const J=()=>{const[e,t]=x.useState(!1);return l.jsxs("nav",{className:"sticky top-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border",children:[l.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"flex justify-between items-center h-16",children:[l.jsx("div",{className:"flex-shrink-0",children:l.jsx(ze,{to:"/",className:"font-heading text-xl font-semibold text-primary hover:text-primary/80 transition-colors",children:"Ink of Sophia"})}),l.jsx("div",{className:"hidden md:block",children:l.jsxs("div",{className:"ml-10 flex items-baseline space-x-8",children:[l.jsxs(ze,{to:"/",className:"text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group",children:["Home",l.jsx("span",{className:"absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"})]}),l.jsxs(ze,{to:"/authors",className:"text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group",children:["Authors",l.jsx("span",{className:"absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"})]}),l.jsxs(ze,{to:"/quotes",className:"text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group",children:["Quotes",l.jsx("span",{className:"absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"})]}),l.jsxs(ze,{to:"/about",className:"text-foreground hover:text-primary transition-colors duration-200 font-body text-sm tracking-wide relative group",children:["About",l.jsx("span",{className:"absolute inset-x-0 bottom-0 h-0.5 bg-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"})]}),l.jsx(ze,{to:"/search",className:"text-muted-foreground hover:text-primary transition-colors",children:l.jsx(sm,{className:"h-4 w-4"})})]})}),l.jsx("div",{className:"md:hidden",children:l.jsx("button",{onClick:()=>t(!e),className:"text-foreground hover:text-primary transition-colors",children:e?l.jsx(am,{className:"h-6 w-6"}):l.jsx(Wx,{className:"h-6 w-6"})})})]})}),e&&l.jsx("div",{className:"md:hidden animate-fade-in",children:l.jsxs("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-background border-t border-border",children:[l.jsx(ze,{to:"/",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-body",onClick:()=>t(!1),children:"Home"}),l.jsx(ze,{to:"/authors",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-body",onClick:()=>t(!1),children:"Authors"}),l.jsx(ze,{to:"/quotes",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-body",onClick:()=>t(!1),children:"Quotes"}),l.jsx(ze,{to:"/about",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-body",onClick:()=>t(!1),children:"About"}),l.jsx(ze,{to:"/search",className:"block px-3 py-2 text-foreground hover:text-primary transition-colors font-body",onClick:()=>t(!1),children:"Search"})]})})]})},Xb=()=>{const e=[{name:"Fyodor Dostoevsky",slug:"dostoevsky",quote:"Pain and suffering are always inevitable for a large intelligence and a deep heart.",description:"Russian master of psychological realism"},{name:"Franz Kafka",slug:"kafka",quote:"A book must be the axe for the frozen sea inside us.",description:"Pioneer of existential literature"},{name:"Oscar Wilde",slug:"wilde",quote:"We are all in the gutter, but some of us are looking at the stars.",description:"Irish wit and aesthetic champion"}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx("section",{className:"relative py-24 lg:py-32 bg-gradient-to-br from-background via-muted/30 to-accent/20",children:l.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[l.jsx("h1",{className:"font-heading text-hero font-bold text-primary mb-6 animate-fade-in tracking-tight",children:"Ink of Sophia"}),l.jsx("p",{className:"font-body text-xl text-muted-foreground mb-12 animate-fade-in-delay max-w-3xl mx-auto leading-relaxed",children:"A literary archive preserving the voices of history's greatest writers, from celebrated masters to forgotten poets awaiting rediscovery."}),l.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up",children:[l.jsx(ze,{to:"/authors",className:"inline-flex items-center px-8 py-3 bg-primary text-primary-foreground font-body font-medium rounded-lg shadow-soft hover:shadow-elegant transition-all duration-300 hover:bg-primary/90",children:"Explore Authors"}),l.jsx("a",{href:"#featured",className:"inline-flex items-center px-8 py-3 border border-primary text-primary font-body font-medium rounded-lg hover:bg-primary/10 transition-all duration-300",children:"Featured Works"})]})]})}),l.jsx("section",{id:"featured",className:"py-16 lg:py-24",children:l.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsx("h2",{className:"font-heading text-heading font-semibold text-primary mb-12 text-center animate-fade-in",children:"Featured Voices"}),l.jsx("div",{className:"grid md:grid-cols-3 gap-8",children:e.map((t,n)=>l.jsx(ze,{to:`/author/${t.slug}`,className:"group animate-scale-in",style:{animationDelay:`${n*.1}s`},children:l.jsxs("div",{className:"bg-card rounded-lg p-8 shadow-soft hover:shadow-elegant transition-all duration-300 border border-border/50 group-hover:border-primary/20",children:[l.jsx("h3",{className:"font-heading text-xl font-semibold text-primary mb-4 group-hover:text-primary/80 transition-colors",children:t.name}),l.jsxs("blockquote",{className:"font-literary text-sm italic text-foreground/80 mb-4 leading-relaxed",children:['"',t.quote,'"']}),l.jsx("p",{className:"font-body text-sm text-muted-foreground",children:t.description}),l.jsxs("div",{className:"mt-6 flex items-center text-primary group-hover:text-primary/80 transition-colors",children:[l.jsx("span",{className:"font-body text-sm font-medium",children:"Read More"}),l.jsx("svg",{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})},t.slug))})]})}),l.jsx("section",{className:"py-16 lg:py-24 bg-muted/30",children:l.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[l.jsx("h2",{className:"font-heading text-heading font-semibold text-primary mb-8 animate-fade-in",children:"Our Mission"}),l.jsxs("div",{className:"prose prose-lg max-w-none",children:[l.jsx("p",{className:"font-body text-body leading-relaxed text-foreground/90 mb-6 animate-fade-in-up",children:"Literature is the mirror of the human soul, reflecting our deepest fears, greatest hopes, and eternal questions. At Ink of Sophia, we believe that the voices of the past hold timeless wisdom for the present."}),l.jsx("p",{className:"font-body text-body leading-relaxed text-foreground/90 animate-fade-in-up",style:{animationDelay:"0.1s"},children:"From the psychological depths of Dostoevsky to the existential anxieties of Kafka, from the wit of Wilde to the confessional power of Plath, we curate and preserve the literary treasures that continue to shape our understanding of what it means to be human."})]})]})}),l.jsx("footer",{className:"py-12 bg-muted/50 border-t border-border/30",children:l.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[l.jsx("h3",{className:"font-heading text-lg font-semibold text-primary mb-4",children:"Ink of Sophia"}),l.jsx("p",{className:"font-body text-sm text-muted-foreground max-w-2xl mx-auto",children:"A curated collection of literary voices, preserving the wisdom and beauty of history's greatest writers for future generations."})]})})]})},Zb=()=>{const e=di();return x.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),l.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),l.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},ek=[{slug:"dostoevsky",name:"Fyodor Dostoevsky",years:"1821–1881",nationality:"Russian",description:"Master of psychological realism and existential philosophy"},{slug:"kafka",name:"Franz Kafka",years:"1883–1924",nationality:"Bohemian",description:"Pioneer of existential literature and absurdist fiction"},{slug:"chekhov",name:"Anton Chekhov",years:"1860–1904",nationality:"Russian",description:"Master of the modern short story and playwright"},{slug:"wilde",name:"Oscar Wilde",years:"1854–1900",nationality:"Irish",description:"Wit, aesthete, and champion of art for art's sake"},{slug:"plath",name:"Sylvia Plath",years:"1932–1963",nationality:"American",description:"Confessional poet of raw emotional intensity"},{slug:"shakespeare",name:"William Shakespeare",years:"1564–1616",nationality:"English",description:"The Bard of Avon, greatest writer in the English language"},{slug:"dickinson",name:"Emily Dickinson",years:"1830–1886",nationality:"American",description:"Reclusive poet who revolutionized American verse"},{slug:"poe",name:"Edgar Allan Poe",years:"1809–1849",nationality:"American",description:"Master of Gothic horror and detective fiction"},{slug:"woolf",name:"Virginia Woolf",years:"1882–1941",nationality:"English",description:"Modernist pioneer and stream-of-consciousness innovator"},{slug:"rilke",name:"Rainer Maria Rilke",years:"1875–1926",nationality:"Austrian",description:"Lyrical poet of spiritual and existential themes"},{slug:"tolstoy",name:"Leo Tolstoy",years:"1828–1910",nationality:"Russian",description:"Epic novelist and moral philosopher"},{slug:"borges",name:"Jorge Luis Borges",years:"1899–1986",nationality:"Argentine",description:"Master of labyrinths, mirrors, and infinite literature"},{slug:"camus",name:"Albert Camus",years:"1913–1960",nationality:"French-Algerian",description:"Absurdist philosopher and Nobel laureate"},{slug:"neruda",name:"Pablo Neruda",years:"1904–1973",nationality:"Chilean",description:"Passionate poet of love, politics, and nature"},{slug:"joyce",name:"James Joyce",years:"1882–1941",nationality:"Irish",description:"Revolutionary modernist who transformed the novel"},{slug:"austen",name:"Jane Austen",years:"1775–1817",nationality:"English",description:"Witty chronicler of Regency society and manners"},{slug:"hemingway",name:"Ernest Hemingway",years:"1899–1961",nationality:"American",description:"Minimalist master of the lost generation"},{slug:"baldwin",name:"James Baldwin",years:"1924–1987",nationality:"American",description:"Powerful voice on race, sexuality, and social justice"},{slug:"morrison",name:"Toni Morrison",years:"1931–2019",nationality:"American",description:"Nobel laureate exploring African American experience"},{slug:"garcia-marquez",name:"Gabriel García Márquez",years:"1927–2014",nationality:"Colombian",description:"Master of magical realism and Latin American literature"},{slug:"orwell",name:"George Orwell",years:"1903–1950",nationality:"English",description:"Dystopian visionary and political satirist"},{slug:"lorca",name:"Federico García Lorca",years:"1898–1936",nationality:"Spanish",description:"Surrealist poet and playwright of Andalusian soul"},{slug:"mishima",name:"Yukio Mishima",years:"1925–1970",nationality:"Japanese",description:"Controversial novelist exploring beauty, death, and tradition"},{slug:"pessoa",name:"Fernando Pessoa",years:"1888–1935",nationality:"Portuguese",description:"Poet of multiple personalities and modernist innovation"},{slug:"basil-daeren",name:"Basil Daeren",years:"1887–1924",nationality:"British",description:"Mystical poet bridging Victorian and modernist traditions"},{slug:"rumi",name:"Jalal ad-Din Rumi",years:"1207–1273",nationality:"Persian",description:"Sufi mystic poet of divine love and spiritual ecstasy"},{slug:"hughes",name:"Langston Hughes",years:"1901–1967",nationality:"American",description:"Voice of the Harlem Renaissance and jazz poetry"},{slug:"atwood",name:"Margaret Atwood",years:"1939–",nationality:"Canadian",description:"Speculative fiction pioneer and feminist literary icon"},{slug:"achebe",name:"Chinua Achebe",years:"1930–2013",nationality:"Nigerian",description:"Postcolonial author who gave voice to African literature"},{slug:"whitman",name:"Walt Whitman",years:"1819–1892",nationality:"American",description:"Democratic poet celebrating the American spirit"}],tk=()=>l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx("main",{className:"py-16 lg:py-24",children:l.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h1",{className:"font-heading text-hero font-bold text-primary mb-6 tracking-tight",children:"Literary Voices"}),l.jsx("p",{className:"font-body text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed",children:"Explore the lives and works of history's most profound literary minds, from celebrated masters to forgotten voices awaiting rediscovery."})]}),l.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:ek.map((e,t)=>l.jsx(ze,{to:`/author/${e.slug}`,className:"group animate-scale-in",style:{animationDelay:`${t*.1}s`},children:l.jsxs("div",{className:"bg-card rounded-lg p-8 shadow-soft hover:shadow-elegant transition-all duration-300 border border-border/50 group-hover:border-primary/20",children:[l.jsx("h3",{className:"font-heading text-xl font-semibold text-primary mb-2 group-hover:text-primary/80 transition-colors",children:e.name}),l.jsxs("p",{className:"font-body text-sm text-muted-foreground mb-3",children:[e.years," • ",e.nationality]}),l.jsx("p",{className:"font-body text-sm text-foreground/80 leading-relaxed",children:e.description}),l.jsxs("div",{className:"mt-6 flex items-center text-primary group-hover:text-primary/80 transition-colors",children:[l.jsx("span",{className:"font-body text-sm font-medium",children:"Read More"}),l.jsx("svg",{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})},e.slug))})]})})]}),nk=[{text:"It is better to light a candle than curse the darkness.",author:"Eleanor Roosevelt",category:"Inspiration"},{text:"The way to get started is to quit talking and begin doing.",author:"Walt Disney",category:"Action"},{text:"Pain is inevitable. Suffering is optional.",author:"Fyodor Dostoevsky",category:"Philosophy"},{text:"A goal is not always meant to be reached, it often serves simply as something to aim at.",author:"Franz Kafka",category:"Purpose"},{text:"The unexamined life is not worth living.",author:"Socrates",category:"Philosophy"},{text:"In the depth of winter, I finally learned that there was in me an invincible summer.",author:"Albert Camus",category:"Resilience"},{text:"What we know is a drop, what we don't know is an ocean.",author:"Isaac Newton",category:"Knowledge"},{text:"Be yourself; everyone else is already taken.",author:"Oscar Wilde",category:"Authenticity"},{text:"I can resist everything except temptation.",author:"Oscar Wilde",category:"Human Nature"},{text:"The best time to plant a tree was 20 years ago. The second best time is now.",author:"Chinese Proverb",category:"Action"},{text:"Mad, bad, and dangerous to know.",author:"Lady Caroline Lamb",category:"Passion"},{text:"I have learned throughout my life as a composer chiefly through my mistakes and pursuits of false assumptions, not by my exposure to founts of wisdom and knowledge.",author:"Igor Stravinsky",category:"Learning"}];function rk(){return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx("div",{className:"container mx-auto px-4 py-12",children:l.jsxs("div",{className:"max-w-4xl mx-auto",children:[l.jsxs("header",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h1",{className:"font-display text-4xl md:text-6xl text-charcoal mb-6",children:"Literary Quotes"}),l.jsx("p",{className:"text-lg text-brown max-w-2xl mx-auto leading-relaxed",children:"A curated collection of wisdom from the great minds of literature, philosophy, and art."})]}),l.jsx("div",{className:"grid gap-8 md:gap-12",children:nk.map((e,t)=>l.jsx("div",{className:"animate-fade-in bg-white/60 backdrop-blur-sm rounded-lg p-8 shadow-soft border border-white/20",style:{animationDelay:`${t*100}ms`},children:l.jsxs("blockquote",{className:"text-center",children:[l.jsxs("p",{className:"text-xl md:text-2xl text-charcoal font-light leading-relaxed mb-6 italic",children:['"',e.text,'"']}),l.jsxs("footer",{children:[l.jsxs("cite",{className:"text-brown font-medium text-lg",children:["— ",e.author]}),l.jsx("div",{className:"mt-2",children:l.jsx("span",{className:"inline-block px-3 py-1 bg-pale-pink/60 text-brown text-sm rounded-full",children:e.category})})]})]})},t))}),l.jsx("div",{className:"text-center mt-16",children:l.jsx("p",{className:"text-brown italic",children:'"The purpose of literature is to turn blood into ink." — T.S. Eliot'})})]})})]})}function ok(){return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx("div",{className:"container mx-auto px-4 py-12",children:l.jsxs("div",{className:"max-w-4xl mx-auto",children:[l.jsxs("header",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h1",{className:"font-display text-4xl md:text-6xl text-charcoal mb-6",children:"About Ink of Sophia"}),l.jsx("p",{className:"text-lg text-brown max-w-2xl mx-auto leading-relaxed",children:"A digital sanctuary for the written word, preserving the voices that shaped our understanding of the human condition."})]}),l.jsxs("div",{className:"prose prose-lg max-w-none",children:[l.jsxs("div",{className:"bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 mb-12 animate-fade-in",children:[l.jsx("h2",{className:"font-display text-3xl text-charcoal mb-6",children:"Our Mission"}),l.jsxs("p",{className:"text-charcoal leading-relaxed mb-6",children:[l.jsx("span",{className:"text-6xl float-left font-display text-brown/40 leading-none mr-3 mt-1",children:"I"}),"nk of Sophia is more than a literary archive—it's a celebration of the transformative power of words. We believe that literature transcends time, offering us windows into different eras, cultures, and perspectives that continue to illuminate our present moment."]}),l.jsx("p",{className:"text-charcoal leading-relaxed mb-6",children:"Named after Sophia, the ancient Greek concept of wisdom, our archive seeks to preserve both the celebrated voices of literary history and those that have been forgotten by time. From Dostoevsky's psychological depths to the imagined brilliance of fictional authors like Basil Daeren, we honor the full spectrum of literary expression."}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"Each author page is crafted as a gentle introduction to their world—a place where readers can discover biographical insights, explore notable works, and find inspiration through carefully selected quotes that capture the essence of their literary voice."})]}),l.jsxs("div",{className:"bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 mb-12 animate-fade-in",style:{animationDelay:"200ms"},children:[l.jsx("h2",{className:"font-display text-3xl text-charcoal mb-6",children:"What You'll Find"}),l.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"font-display text-xl text-brown mb-3",children:"Author Profiles"}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"Intimate biographical sketches that reveal the person behind the pen, exploring the experiences that shaped their literary voice."})]}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-display text-xl text-brown mb-3",children:"Notable Works"}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"Curated selections of each author's most significant contributions, with contextual notes to guide your literary journey."})]}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-display text-xl text-brown mb-3",children:"Literary Quotes"}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"Carefully chosen passages that distill the essence of each author's wisdom and worldview."})]}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-display text-xl text-brown mb-3",children:"Forgotten Voices"}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"Special attention to overlooked and underrepresented authors whose words deserve to be remembered and celebrated."})]})]})]}),l.jsxs("div",{className:"bg-white/60 backdrop-blur-sm rounded-lg p-8 md:p-12 shadow-soft border border-white/20 animate-fade-in",style:{animationDelay:"400ms"},children:[l.jsx("h2",{className:"font-display text-3xl text-charcoal mb-6",children:"Our Philosophy"}),l.jsx("p",{className:"text-charcoal leading-relaxed mb-6",children:"We believe that literature is not merely entertainment—it's a form of empathy made manifest. Through the careful study of literary voices, we develop a deeper understanding of the human experience across cultures and centuries."}),l.jsx("p",{className:"text-charcoal leading-relaxed mb-6",children:"Our archive is designed as a quiet space for contemplation, free from the noise of modern digital life. Here, you can slow down, reflect, and engage with texts that have moved readers for generations."}),l.jsxs("blockquote",{className:"text-center py-8",children:[l.jsx("p",{className:"text-xl text-brown italic",children:'"The real question is not whether machines think but whether men do."'}),l.jsx("cite",{className:"text-charcoal text-sm",children:"— B.F. Skinner"})]}),l.jsx("p",{className:"text-charcoal leading-relaxed",children:"In an age of artificial intelligence and rapid technological change, we remain committed to preserving the irreplaceable value of human creativity and the written word."})]})]})]})})]})}const ik=[{type:"author",title:"Fyodor Dostoevsky",url:"/author/dostoevsky",description:"Russian novelist and philosopher"},{type:"author",title:"Franz Kafka",url:"/author/kafka",description:"Czech writer known for surreal fiction"},{type:"author",title:"Anton Chekhov",url:"/author/chekhov",description:"Russian playwright and short story writer"},{type:"author",title:"Oscar Wilde",url:"/author/wilde",description:"Irish poet and playwright"},{type:"author",title:"Sylvia Plath",url:"/author/plath",description:"American poet and novelist"},{type:"author",title:"William Shakespeare",url:"/author/shakespeare",description:"English playwright and poet"},{type:"page",title:"Quotes",url:"/quotes",description:"Collection of literary quotes"},{type:"page",title:"About",url:"/about",description:"About Ink of Sophia"},{type:"page",title:"Authors",url:"/authors",description:"Browse all authors"},{type:"keyword",title:"Russian Literature",url:"/authors",description:"Dostoevsky, Chekhov, and more"},{type:"keyword",title:"Poetry",url:"/authors",description:"Plath, Shakespeare, and others"},{type:"keyword",title:"Philosophy",url:"/quotes",description:"Philosophical quotes and insights"}];function sk(){const[e,t]=x.useState(""),[n,r]=x.useState([]),o=i=>{if(t(i),i.trim()===""){r([]);return}const s=ik.filter(a=>a.title.toLowerCase().includes(i.toLowerCase())||a.description.toLowerCase().includes(i.toLowerCase()));r(s)};return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx("div",{className:"container mx-auto px-4 py-12",children:l.jsxs("div",{className:"max-w-4xl mx-auto",children:[l.jsxs("header",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h1",{className:"font-display text-4xl md:text-6xl text-charcoal mb-6",children:"Search"}),l.jsx("p",{className:"text-lg text-brown max-w-2xl mx-auto leading-relaxed mb-8",children:"Discover authors, quotes, and literary insights across our archive."}),l.jsxs("div",{className:"relative max-w-2xl mx-auto",children:[l.jsx(sm,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-brown h-5 w-5"}),l.jsx("input",{type:"text",placeholder:"Search for authors, quotes, or topics...",value:e,onChange:i=>o(i.target.value),className:"w-full pl-12 pr-4 py-4 text-lg border border-brown/20 rounded-lg bg-white/60 backdrop-blur-sm focus:outline-none focus:border-brown/40 focus:ring-2 focus:ring-brown/20"})]})]}),e&&l.jsxs("div",{className:"animate-fade-in",children:[l.jsxs("h2",{className:"text-2xl font-display text-charcoal mb-6",children:['Search Results for "',e,'"']}),n.length===0?l.jsx("div",{className:"bg-white/60 backdrop-blur-sm rounded-lg p-8 shadow-soft border border-white/20 text-center",children:l.jsx("p",{className:"text-brown text-lg",children:"No results found. Try searching for an author name, literary term, or topic."})}):l.jsx("div",{className:"grid gap-4",children:n.map((i,s)=>l.jsx(ze,{to:i.url,className:"block bg-white/60 backdrop-blur-sm rounded-lg p-6 shadow-soft border border-white/20 hover:bg-white/80 transition-all duration-300 hover:shadow-lg",children:l.jsxs("div",{className:"flex items-start justify-between",children:[l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-display text-charcoal mb-2",children:i.title}),l.jsx("p",{className:"text-brown",children:i.description})]}),l.jsx("span",{className:"px-3 py-1 bg-pale-pink/60 text-brown text-sm rounded-full capitalize",children:i.type})]})},s))})]}),!e&&l.jsxs("div",{className:"animate-fade-in",children:[l.jsx("h2",{className:"text-2xl font-display text-charcoal mb-6 text-center",children:"Popular Searches"}),l.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:["Dostoevsky","Kafka","Philosophy","Poetry","Russian Literature","Quotes"].map((i,s)=>l.jsx("button",{onClick:()=>o(i),className:"bg-white/60 backdrop-blur-sm rounded-lg p-4 shadow-soft border border-white/20 hover:bg-white/80 transition-all duration-300 text-charcoal hover:text-brown",children:i},s))})]})]})})]})}const ee=({name:e,quote:t,years:n})=>l.jsxs("section",{className:"relative py-20 lg:py-32 bg-gradient-to-br from-background via-muted/30 to-accent/20",children:[l.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[l.jsx("h1",{className:"font-heading text-hero font-bold text-primary mb-6 animate-fade-in tracking-tight",children:e}),l.jsx("p",{className:"font-body text-lg text-muted-foreground mb-8 animate-fade-in-delay tracking-wide",children:n}),l.jsxs("blockquote",{className:"relative animate-fade-in-up",children:[l.jsx("div",{className:"absolute -top-4 -left-4 text-6xl text-accent/30 font-heading leading-none",children:'"'}),l.jsx("div",{className:"absolute -bottom-8 -right-4 text-6xl text-accent/30 font-heading leading-none",children:'"'}),l.jsx("p",{className:"font-literary text-xl lg:text-2xl italic text-foreground/90 leading-relaxed max-w-3xl mx-auto px-8 py-4",children:t})]})]}),l.jsx("div",{className:"absolute top-1/2 left-4 w-1 h-16 bg-gradient-to-b from-primary/20 to-transparent rounded-full transform -translate-y-1/2"}),l.jsx("div",{className:"absolute top-1/2 right-4 w-1 h-16 bg-gradient-to-b from-primary/20 to-transparent rounded-full transform -translate-y-1/2"})]}),ak=({imageSrc:e,imageAlt:t,authorName:n})=>l.jsx("section",{className:"py-16 bg-gradient-to-br from-accent/10 to-muted/20",children:l.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:l.jsxs("div",{className:"relative inline-block animate-scale-in",children:[l.jsxs("div",{className:"relative",children:[l.jsx("img",{src:e,alt:t,className:"w-64 h-80 md:w-80 md:h-96 object-cover rounded-lg shadow-elegant mx-auto filter grayscale hover:grayscale-0 transition-all duration-500"}),l.jsx("div",{className:"absolute inset-0 rounded-lg border-2 border-primary/20 pointer-events-none"}),l.jsx("div",{className:"absolute -inset-2 rounded-lg border border-accent/30 pointer-events-none"})]}),l.jsx("p",{className:"font-body text-sm text-muted-foreground mt-4 italic",children:n})]})})}),te=({title:e,content:t})=>l.jsx("section",{className:"py-16 lg:py-24",children:l.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsx("h2",{className:"font-heading text-heading font-semibold text-primary mb-8 text-center animate-fade-in",children:e}),l.jsx("div",{className:"prose prose-lg max-w-none",children:t.map((n,r)=>l.jsx("p",{className:`font-body text-body leading-relaxed text-foreground/90 mb-6 animate-fade-in-up ${r===0?"first-letter:text-6xl first-letter:font-heading first-letter:text-primary first-letter:float-left first-letter:mr-2 first-letter:mt-1 first-letter:leading-none":""}`,style:{animationDelay:`${r*.1}s`},children:n},r))})]})}),ne=({works:e})=>l.jsx("section",{className:"py-16 lg:py-24 bg-gradient-to-br from-background via-secondary/20 to-accent/10",children:l.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h2",{className:"font-heading text-heading font-bold text-primary mb-4 tracking-tight",children:"Featured Works"}),l.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-primary via-accent to-primary mx-auto rounded-full opacity-60"})]}),l.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8",children:e.map((t,n)=>l.jsxs("div",{className:"group relative bg-card/80 backdrop-blur-sm rounded-2xl p-8 shadow-soft hover:shadow-elegant transition-all duration-500 animate-scale-in border border-border/30 hover:border-primary/20 hover:bg-card/90 overflow-hidden",style:{animationDelay:`${n*.1}s`},children:[l.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"}),l.jsxs("div",{className:"relative z-10",children:[l.jsxs("div",{className:"flex items-start justify-between mb-6",children:[l.jsx("h3",{className:"font-heading text-xl font-semibold text-primary leading-tight group-hover:text-primary/90 transition-colors duration-300 pr-4",children:t.title}),l.jsx("div",{className:"flex-shrink-0",children:l.jsx("span",{className:"inline-flex items-center justify-center w-12 h-8 text-xs font-medium text-primary bg-gradient-to-r from-accent/30 to-secondary/40 rounded-full border border-primary/10",children:t.year})})]}),l.jsx("p",{className:"font-body text-foreground/70 leading-relaxed text-sm group-hover:text-foreground/85 transition-colors duration-300",children:t.description}),l.jsx("div",{className:"mt-6 h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent"})]}),l.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-accent/40 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"})]},t.title))})]})}),re=({quotes:e})=>l.jsx("section",{className:"py-16 lg:py-24 bg-gradient-to-b from-background to-secondary/10",children:l.jsxs("div",{className:"max-w-5xl mx-auto px-4 sm:px-6 lg:px-8",children:[l.jsxs("div",{className:"text-center mb-16 animate-fade-in",children:[l.jsx("h2",{className:"font-heading text-heading font-bold text-primary mb-4 tracking-tight",children:"Literary Reflections"}),l.jsx("div",{className:"w-16 h-1 bg-gradient-to-r from-primary via-accent to-primary mx-auto rounded-full opacity-60"})]}),l.jsx("div",{className:"space-y-8 lg:space-y-12",children:e.map((t,n)=>l.jsx("div",{className:"group relative animate-fade-in-up",style:{animationDelay:`${n*.2}s`},children:l.jsxs("div",{className:"relative bg-gradient-to-br from-card/90 via-card/70 to-card/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-soft hover:shadow-elegant transition-all duration-500 border border-border/20 hover:border-primary/15 overflow-hidden",children:[l.jsx("div",{className:"absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-accent/20 to-primary/10 rounded-full blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"}),l.jsx("div",{className:"absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-tl from-primary/10 to-accent/20 rounded-full blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-500"}),l.jsx("div",{className:"absolute top-6 left-8 text-6xl lg:text-7xl text-accent/30 font-serif leading-none",children:'"'}),l.jsx("div",{className:"absolute bottom-6 right-8 text-6xl lg:text-7xl text-accent/30 font-serif leading-none",children:'"'}),l.jsxs("blockquote",{className:"relative z-10",children:[l.jsx("p",{className:"font-heading text-lg lg:text-xl text-foreground leading-relaxed text-center px-8 lg:px-16 italic group-hover:text-foreground/95 transition-colors duration-300",children:t.text}),t.source&&l.jsx("footer",{className:"mt-8 text-center",children:l.jsxs("cite",{className:"font-body text-sm text-muted-foreground/80",children:["— ",t.source]})})]}),l.jsx("div",{className:"absolute inset-4 bg-gradient-to-br from-accent/5 via-transparent to-primary/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"})]})},n))})]})}),lk="/assets/dostoevsky-portrait-kSpDqhZP.jpg",uk=()=>{const e=["Fyodor Mikhailovich Dostoevsky was born in Moscow in 1821 into a middle-class family. His father was a doctor who worked at a hospital for the poor, instilling in young Fyodor an early awareness of human suffering and social inequality. This exposure to the darker aspects of human existence would profoundly influence his literary worldview.","After graduating from military engineering school, Dostoevsky abandoned his engineering career to pursue writing. His first novel, 'Poor Folk' (1846), brought him immediate literary fame. However, his involvement with a revolutionary circle led to his arrest in 1849 and a death sentence that was commuted to four years of hard labor in Siberia, followed by mandatory military service.","The Siberian experience transformed Dostoevsky's understanding of human nature and spirituality. Upon his return to literary life, he produced his greatest works, including 'Crime and Punishment,' 'The Brothers Karamazov,' and 'The Idiot.' These novels explored themes of guilt, redemption, free will, and the conflict between faith and reason, establishing him as one of the greatest psychological novelists in world literature."],t=[{title:"Crime and Punishment",year:1866,description:"A psychological thriller exploring the mind of Raskolnikov, a young man who commits murder and grapples with guilt and redemption."},{title:"The Brothers Karamazov",year:1880,description:"Dostoevsky's final novel, a philosophical exploration of faith, doubt, and morality through the story of three brothers."},{title:"The Idiot",year:1869,description:"The story of Prince Myshkin, a Christ-like figure whose goodness is misunderstood in a corrupt society."},{title:"Demons",year:1872,description:"A political novel examining the destructive nature of radical ideology and revolutionary fervor."},{title:"Notes from Underground",year:1864,description:"A groundbreaking novella that prefigured existentialist philosophy through its unreliable narrator."}],n=[{text:"Pain and suffering are always inevitable for a large intelligence and a deep heart.",source:"Crime and Punishment"},{text:"The mystery of human existence lies not in just staying alive, but in finding something to live for.",source:"The Brothers Karamazov"},{text:"Can a man of perception respect himself at all?"}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"Fyodor Dostoevsky",quote:"The soul is healed by being with children",years:"1821 – 1881"}),l.jsx(ak,{imageSrc:lk,imageAlt:"Portrait of Fyodor Dostoevsky",authorName:"Fyodor Mikhailovich Dostoevsky"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})},ck=()=>{const e=["Franz Kafka was born in Prague in 1883 to a middle-class Jewish family. His relationship with his domineering father deeply influenced his work, creating themes of alienation, guilt, and the struggle against incomprehensible authority that would define his literary voice. Despite studying law and working in insurance, Kafka's true passion lay in writing.","Most of Kafka's major works were published posthumously, as he had instructed his friend Max Brod to destroy his manuscripts. Fortunately, Brod disobeyed this request, preserving for posterity some of the most influential literature of the 20th century. Kafka's writing style, characterized by surreal situations and bureaucratic nightmares, gave birth to the term 'Kafkaesque.'","Kafka died young at 40 from tuberculosis, leaving behind a body of work that would profoundly influence existentialist philosophy and modernist literature. His exploration of absurdity, anxiety, and the human condition resonates deeply with readers facing the complexities of modern life."],t=[{title:"The Metamorphosis",year:1915,description:"The story of Gregor Samsa, who wakes up transformed into a giant insect, exploring themes of alienation and family obligation."},{title:"The Trial",year:1925,description:"Josef K. is arrested and prosecuted by an inaccessible authority for an unspecified crime, a nightmarish vision of bureaucracy."},{title:"The Castle",year:1926,description:"A land surveyor's futile attempts to gain access to a mysterious castle, representing the search for meaning and belonging."},{title:"Amerika",year:1927,description:"The story of young Karl Rossmann's experiences in America, a satirical view of the promised land of opportunity."},{title:"A Hunger Artist",year:1922,description:"A disturbing tale of a professional faster whose art becomes increasingly meaningless to society."}],n=[{text:"I cannot make you understand. I cannot make anyone understand what is happening inside me. I cannot even explain it to myself.",source:"The Metamorphosis"},{text:"A book must be the axe for the frozen sea inside us."},{text:"Don't bend; don't water it down; don't try to make it logical; don't edit your own soul according to the fashion. Rather, follow your most intense obsessions mercilessly."}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"Franz Kafka",quote:"A book must be the axe for the frozen sea inside us",years:"1883 – 1924"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})},dk=()=>{const e=["Anton Pavlovich Chekhov was born in Taganrog, Russia, in 1860, the son of a grocer and grandson of a serf. While studying medicine at Moscow University, he began writing short humorous sketches to support his family financially. This dual career as physician and writer profoundly influenced his compassionate and realistic portrayal of human nature.","Chekhov revolutionized both the short story and modern drama through his subtle, understated style. His stories focus on ordinary people in everyday situations, revealing profound truths about the human condition through seemingly simple narratives. His famous dictum was that if a gun appears in the first act, it must be fired by the third—a principle of dramatic economy that bears his name.","Despite dying young at 44 from tuberculosis, Chekhov left an indelible mark on world literature. His four major plays—'The Seagull,' 'Uncle Vanya,' 'Three Sisters,' and 'The Cherry Orchard'—remain staples of theater worldwide, while his short stories continue to influence writers across all genres."],t=[{title:"The Cherry Orchard",year:1904,description:"His final play about the decline of the Russian aristocracy, blending comedy and tragedy in masterful fashion."},{title:"Three Sisters",year:1901,description:"A poignant drama about the Prozorov sisters' longing to return to Moscow and escape provincial life."},{title:"Uncle Vanya",year:1897,description:"A tragicomedy exploring themes of unrequited love, missed opportunities, and the meaning of existence."},{title:"The Seagull",year:1896,description:"A meditation on love, art, and the nature of theater, initially a failure but now considered a masterpiece."},{title:"The Lady with the Dog",year:1899,description:"Perhaps his greatest short story, about a chance encounter that develops into profound love."}],n=[{text:"Don't tell me the moon is shining; show me the glint of light on broken glass."},{text:"Any idiot can face a crisis - it's day to day living that wears you out."},{text:"Medicine is my lawful wife and literature my mistress; when I get tired of one, I spend the night with the other."}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"Anton Chekhov",quote:"Don't tell me the moon is shining; show me the glint of light on broken glass",years:"1860 – 1904"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})},fk=()=>{const e=["Oscar Fingal O'Flahertie Wills Wilde was born in Dublin in 1854 to intellectual parents—his father was a renowned surgeon and his mother a poet and Irish nationalist. Educated at Trinity College Dublin and later Oxford, Wilde became associated with the aesthetic movement, championing 'art for art's sake' and developing a reputation for his wit, flamboyant style, and brilliant conversation.","Wilde's career flourished in London's literary society, where he became famous for his plays, poetry, and essays. His only novel, 'The Picture of Dorian Gray,' caused scandal for its perceived decadence and homoerotic undertones. His theatrical comedies, particularly 'The Importance of Being Earnest,' showcased his masterful use of paradox and epigram.","In 1895, Wilde's life took a tragic turn when he was imprisoned for 'gross indecency' due to his relationship with Lord Alfred Douglas. His time in Reading Gaol inspired 'De Profundis' and 'The Ballad of Reading Gaol.' After his release, he lived in exile in Paris under an assumed name, dying in poverty in 1900 at age 46, but leaving behind a legacy of wit, beauty, and tragic heroism."],t=[{title:"The Picture of Dorian Gray",year:1890,description:"His only novel, a Gothic tale of beauty, corruption, and the price of eternal youth."},{title:"The Importance of Being Earnest",year:1895,description:"A brilliant comedy of manners that satirizes Victorian society with wit and paradox."},{title:"An Ideal Husband",year:1895,description:"A play exploring themes of political corruption, blackmail, and moral compromise."},{title:"The Ballad of Reading Gaol",year:1898,description:"A haunting poem about his prison experience and the execution of a fellow inmate."},{title:"De Profundis",year:1905,description:"A deeply personal letter written from prison, reflecting on suffering, art, and love."}],n=[{text:"We are all in the gutter, but some of us are looking at the stars.",source:"Lady Windermere's Fan"},{text:"Be yourself; everyone else is already taken."},{text:"I can resist everything except temptation.",source:"Lady Windermere's Fan"}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"Oscar Wilde",quote:"We are all in the gutter, but some of us are looking at the stars",years:"1854 – 1900"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})},hk=()=>{const e=["Sylvia Plath was born in Boston in 1932 to a German immigrant father who was a professor of entomology and a mother who was a teacher. Her father's death when she was eight profoundly affected her, becoming a recurring theme in her poetry. An exceptional student, she attended Smith College on scholarship, where she excelled academically while struggling with severe depression.","After a suicide attempt during college, Plath underwent electroconvulsive therapy, an experience that would later inform her semi-autobiographical novel 'The Bell Jar.' She won a Fulbright scholarship to Cambridge University, where she met and married fellow poet Ted Hughes in 1956. Their tempestuous relationship would deeply influence her work and personal life.","Plath's poetry evolved from conventional forms to the powerful, confessional style that made her famous. Her final poems, written in the months before her death in 1963, are considered among the finest in the English language. Though she died by suicide at 30, her posthumously published 'Ariel' established her as one of the most important poets of the 20th century, earning her a Pulitzer Prize."],t=[{title:"Ariel",year:1965,description:"Her most famous collection, containing some of her most powerful and disturbing poems about death, rebirth, and identity."},{title:"The Bell Jar",year:1963,description:"Her semi-autobiographical novel about a young woman's descent into mental illness, a classic of feminist literature."},{title:"The Colossus",year:1960,description:"Her first poetry collection, showing her technical mastery and the development of her distinctive voice."},{title:"Crossing the Water",year:1971,description:"Transitional poems that bridge her early work and the fierce final poems of her career."},{title:"Winter Trees",year:1971,description:"Late poems that explore themes of motherhood, nature, and psychological states."}],n=[{text:"The worst enemy to creativity is self-doubt."},{text:"I took a deep breath and listened to the old brag of my heart. I am, I am, I am.",source:"The Bell Jar"},{text:"Everything in life is writable about if you have the outgoing guts to do it, and the imagination to improvise."}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"Sylvia Plath",quote:"I took a deep breath and listened to the old brag of my heart. I am, I am, I am",years:"1932 – 1963"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})},pk=()=>{const e=["William Shakespeare was born in Stratford-upon-Avon in 1564 to John Shakespeare, a glove-maker and alderman, and Mary Arden, from a well-to-do farming family. Little is known about his early education, though he likely attended the King's New School in Stratford, where he would have learned Latin and classical literature that would later influence his plays.","In 1582, Shakespeare married Anne Hathaway, eight years his senior, and they had three children together. By the early 1590s, he had moved to London and established himself as both an actor and playwright. He became a founding member of the Lord Chamberlain's Men (later the King's Men), one of the leading theatrical companies of the day.","Shakespeare's career spanned roughly 25 years, during which he wrote approximately 37 plays and 154 sonnets. His works encompass tragedies, comedies, and histories that explore the full range of human experience with unparalleled psychological insight and poetic beauty. He died in 1616, leaving behind a body of work that has influenced virtually every subsequent writer in the English language and established him as the greatest playwright in literary history."],t=[{title:"Hamlet",year:1601,description:"The tragic tale of the Prince of Denmark's quest for revenge, featuring the most famous soliloquy in English literature."},{title:"Romeo and Juliet",year:1597,description:"The archetypal story of young love destroyed by family feuds, one of the most performed plays in history."},{title:"Macbeth",year:1606,description:"A dark tragedy of ambition and guilt, exploring the psychological consequences of unchecked power."},{title:"A Midsummer Night's Dream",year:1596,description:"A magical comedy weaving together multiple love stories with fairy enchantment and theatrical illusion."},{title:"King Lear",year:1606,description:"A brutal tragedy of family betrayal and the abdication of power, considered by many his greatest work."}],n=[{text:"To be, or not to be, that is the question.",source:"Hamlet"},{text:"All the world's a stage, and all the men and women merely players.",source:"As You Like It"},{text:"The course of true love never did run smooth.",source:"A Midsummer Night's Dream"}];return l.jsxs("div",{className:"min-h-screen bg-background",children:[l.jsx(J,{}),l.jsx(ee,{name:"William Shakespeare",quote:"All the world's a stage, and all the men and women merely players",years:"1564 – 1616"}),l.jsx(te,{title:"Life & Legacy",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})};function mk(){const e=["Emily Elizabeth Dickinson (1830-1886) was an American poet who lived most of her life in almost total seclusion in Amherst, Massachusetts. Known for her reclusive nature, she wrote nearly 1,800 poems, though fewer than a dozen were published during her lifetime.","Her poetry is characterized by short lines, slant rhyme, unconventional capitalization and punctuation, and themes of death, immortality, and nature. Her unique style and profound insights into the human condition have made her one of the most important American poets.","Dickinson's work was largely unknown during her lifetime, but her sister-in-law discovered her poems after her death and ensured their publication, revealing a voice of extraordinary power and originality that continues to resonate with readers today."],t=[{title:"Poems by Emily Dickinson",year:1890,description:"First posthumous collection, edited by Mabel Loomis Todd"},{title:"Poems: Second Series",year:1891,description:"Additional poems published by family"},{title:"The Complete Poems of Emily Dickinson",year:1955,description:"Comprehensive collection edited by Thomas H. Johnson"},{title:"The Manuscript Books of Emily Dickinson",year:1981,description:"Facsimile edition preserving her original presentation"}],n=[{text:"I'm Nobody! Who are you? Are you - Nobody - Too? Then there's a pair of us!"},{text:"Hope is the thing with feathers that perches in the soul."},{text:"Because I could not stop for Death, He kindly stopped for me."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Emily Dickinson",quote:"I dwell in possibility.",years:"1830-1886"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function gk(){const e=["Edgar Allan Poe (1809-1849) was an American writer, poet, editor, and literary critic best known for his tales of mystery and the macabre. Born in Boston to actors, he became an orphan at a young age and was taken in by John Allan, though never formally adopted.","Poe is credited with inventing the detective fiction genre and contributing significantly to the emerging science fiction genre. His work often explored themes of death, decay, and lost love, reflecting his own troubled life marked by alcoholism, financial difficulties, and personal losses.","Despite his relatively short life and limited commercial success during his lifetime, Poe's influence on literature has been immense. His critical theories about poetry and short stories have shaped literary criticism, and his dark, atmospheric tales continue to captivate readers worldwide."],t=[{title:"The Raven and Other Poems",year:1845,description:"Collection featuring his most famous poem"},{title:"Tales of the Grotesque and Arabesque",year:1840,description:"First collection of short stories"},{title:"The Murders in the Rue Morgue",year:1841,description:"Considered the first modern detective story"},{title:"The Tell-Tale Heart",year:1843,description:"Classic tale of guilt and madness"},{title:"The Fall of the House of Usher",year:1839,description:"Gothic masterpiece of atmospheric horror"}],n=[{text:"All that we see or seem is but a dream within a dream."},{text:"I became insane, with long intervals of horrible sanity."},{text:"The death of a beautiful woman is, unquestionably, the most poetical topic in the world."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Edgar Allan Poe",quote:"Deep into that darkness peering, long I stood there wondering, fearing.",years:"1809-1849"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function yk(){const e=["Virginia Woolf (1882-1941) was an English writer and one of the foremost modernist literary figures of the twentieth century. Born into a literary family in London, she was largely self-educated but became one of the most innovative writers of her time.","Woolf was a central figure in the Bloomsbury Group of intellectuals and artists. She pioneered stream-of-consciousness narrative techniques and explored themes of time, memory, and the inner lives of characters. Her work often examined the role of women in society and the nature of consciousness.","Throughout her life, Woolf struggled with mental illness, which both tormented and informed her writing. Her novels broke new ground in psychological realism and narrative technique, influencing generations of writers and establishing her as a key figure in feminist literature."],t=[{title:"Mrs. Dalloway",year:1925,description:"Modernist novel following a single day in London"},{title:"To the Lighthouse",year:1927,description:"Experimental novel exploring time and memory"},{title:"Orlando",year:1928,description:"Fantastical biography spanning centuries"},{title:"The Waves",year:1931,description:"Highly experimental novel told through soliloquies"},{title:"A Room of One's Own",year:1929,description:"Extended essay on women and fiction"}],n=[{text:"For most of history, Anonymous was a woman."},{text:"Yet it is in our idleness, in our dreams, that the submerged truth sometimes comes to the top."},{text:"A woman must have money and a room of her own if she is to write fiction."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Virginia Woolf",quote:"What is the meaning of life? That was all—a simple question; one that tended to close in on one with years.",years:"1882-1941"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function vk(){const e=["Rainer Maria Rilke (1875-1926) was a Bohemian-Austrian poet and novelist widely recognized as one of the most lyrically intense German-language poets. Born in Prague, he traveled extensively throughout Europe, living in Germany, Russia, Spain, and France.","Rilke's work is characterized by mystical undertones, existential themes, and an exploration of the relationship between life and death. His poetry often deals with themes of God, death, beauty, and suffering, expressed through highly metaphorical and symbolic language.","His major works, including the 'Duino Elegies' and 'Sonnets to Orpheus,' are considered pinnacles of 20th-century poetry. Rilke believed that art should capture the essence of human experience and transform suffering into beauty through the power of creative expression."],t=[{title:"Duino Elegies",year:1923,description:"Ten elegies exploring existence, angels, and human consciousness"},{title:"Sonnets to Orpheus",year:1923,description:"Fifty-five sonnets on transformation and the nature of poetry"},{title:"Letters to a Young Poet",year:1929,description:"Posthumously published correspondence offering writing advice"},{title:"The Book of Hours",year:1905,description:"Early collection of mystical, religious poetry"},{title:"New Poems",year:1908,description:"Two volumes showcasing his mature poetic style"}],n=[{text:"Perhaps all the dragons in our lives are princesses who are only waiting to see us act, just once, with courage."},{text:"The only journey is the one within."},{text:"Let everything happen to you: beauty and terror. Just keep going. No feeling is final."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Rainer Maria Rilke",quote:"Be patient toward all that is unsolved in your heart and try to love the questions themselves.",years:"1875-1926"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function xk(){const e=["Leo Tolstoy (1828-1910) was a Russian writer widely regarded as one of the greatest authors of all time. Born into an aristocratic family, he would later renounce his privileged lifestyle to pursue a simpler, more spiritual existence based on his moral and religious beliefs.","Tolstoy's early works were largely autobiographical, but he achieved worldwide fame with his epic novels 'War and Peace' and 'Anna Karenina.' These works explore complex themes of war, peace, love, family, and faith while providing panoramic views of Russian society.","In his later years, Tolstoy underwent a spiritual crisis that led him to develop his own interpretation of Christianity, emphasizing nonviolence, simplicity, and moral purification. His philosophical writings and advocacy for social reform influenced figures like Mahatma Gandhi and Martin Luther King Jr."],t=[{title:"War and Peace",year:1869,description:"Epic novel set during the Napoleonic Wars"},{title:"Anna Karenina",year:1878,description:"Tragic story of love, society, and moral conflict"},{title:"The Death of Ivan Ilyich",year:1886,description:"Novella exploring mortality and the meaning of life"},{title:"Resurrection",year:1899,description:"Novel about moral and spiritual redemption"},{title:"A Confession",year:1882,description:"Autobiographical work on his spiritual crisis"}],n=[{text:"Everyone thinks of changing the world, but no one thinks of changing himself."},{text:"All happy families are alike; each unhappy family is unhappy in its own way."},{text:"The two most powerful warriors are patience and time."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Leo Tolstoy",quote:"If you want to be happy, be.",years:"1828-1910"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function wk(){const e=["Jorge Luis Borges (1899-1986) was an Argentine short-story writer, essayist, poet, and translator whose work has been fundamental to the development of modern literature. Born in Buenos Aires, he was raised in a literary household and became fluent in several languages.","Borges is best known for his intricate, labyrinthine short stories that explore themes of infinity, mirrors, labyrinths, and the nature of reality. His work often blurs the boundaries between fiction and reality, creating fantastical worlds grounded in philosophical inquiry.","Despite being largely ignored by the Nobel Prize committee, Borges is considered one of the most important writers of the 20th century. His influence on contemporary literature, particularly magical realism and postmodern fiction, has been profound and enduring."],t=[{title:"Labyrinths",year:1962,description:"Collection including 'The Library of Babel' and 'The Garden of Forking Paths'"},{title:"Ficciones",year:1944,description:"Collection of interconnected short stories"},{title:"The Aleph",year:1949,description:"Stories exploring infinity and metaphysical concepts"},{title:"A Universal History of Iniquity",year:1935,description:"His first collection of short stories"},{title:"Dreamtigers",year:1960,description:"Mix of poetry and prose exploring memory and identity"}],n=[{text:"I have always imagined that Paradise will be a kind of library."},{text:"We accept reality so readily—perhaps because we intuitively suspect that nothing is real."},{text:"Time is the substance from which I am made. Time is a river which carries me along, but I am the river."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Jorge Luis Borges",quote:"The universe is not only stranger than we imagine, it is stranger than we can imagine.",years:"1899-1986"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function bk(){const e=["Albert Camus (1913-1960) was a French-Algerian philosopher, author, and journalist who became one of the most significant thinkers of the 20th century. Born into poverty in colonial Algeria, his experiences there deeply influenced his worldview and writing.","Camus is most closely associated with existentialism and absurdism, though he rejected the existentialist label. His philosophy centered on the concept of the absurd—the conflict between human desire for meaning and the meaningless silence of the universe.","In 1957, at age 44, Camus became the second-youngest recipient of the Nobel Prize in Literature. His works explore themes of suicide, rebellion, and the human condition, advocating for living fully despite life's inherent meaninglessness."],t=[{title:"The Stranger",year:1942,description:"Novel exploring alienation and the absurd"},{title:"The Plague",year:1947,description:"Allegorical novel about a plague outbreak in Algeria"},{title:"The Myth of Sisyphus",year:1942,description:"Philosophical essay on the absurd and suicide"},{title:"The Rebel",year:1951,description:"Essay examining revolution and rebellion"},{title:"The Fall",year:1956,description:"Philosophical novel told as a monologue"}],n=[{text:"In the midst of winter, I found there was, within me, an invincible summer."},{text:"The struggle itself toward the heights is enough to fill a man's heart."},{text:"There is but one truly serious philosophical problem, and that is suicide."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Albert Camus",quote:"I rebel; therefore I exist.",years:"1913-1960"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function kk(){const e=["Pablo Neruda (1904-1973) was a Chilean poet and diplomat who became one of the most influential poets of the 20th century. Born Ricardo Eliécer Neftalí Reyes Basoalto, he adopted his pen name in honor of the Czech poet Jan Neruda.","Neruda's poetry evolved through several distinct phases, from the melancholic romanticism of his early work to the surrealist period, and finally to a more accessible, politically engaged style. His work often celebrated love, nature, and the common people of Latin America.","A committed communist and diplomat, Neruda served as a senator and consul in various countries. He was awarded the Nobel Prize in Literature in 1971, and his death in 1973, shortly after Pinochet's coup in Chile, remains controversial and subject to investigation."],t=[{title:"Twenty Love Poems and a Song of Despair",year:1924,description:"Early collection of passionate love poetry"},{title:"Residence on Earth",year:1935,description:"Surrealist poetry exploring existential themes"},{title:"Canto General",year:1950,description:"Epic poem celebrating Latin American history and landscape"},{title:"Elementary Odes",year:1954,description:"Simple, celebratory poems about everyday objects"},{title:"100 Love Sonnets",year:1959,description:"Classical sonnets dedicated to his wife Matilde"}],n=[{text:"Love is so short, forgetting is so long."},{text:"You can cut all the flowers but you cannot keep spring from coming."},{text:"I love you without knowing how, or when, or from where. I love you straightforwardly, without complexities or pride."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Pablo Neruda",quote:"Tonight I can write the saddest lines.",years:"1904-1973"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Sk(){const e=["Daeren, also known as Basil Daeren, is a writer best remembered for his haunting romantic tragedies. His true identity and life story remain largely unknown, shrouded in the same mystery that permeates his literary works.","He is most popularly associated with the emotional novella 'Frozen Eyes,' considered by many readers as a deeply tragic portrayal of imaginary love. This work captures the essence of longing and loss with an intensity that has resonated with readers across generations.","His other known works include 'Quill in the Flame' and 'Nights of Roamers,' both of which are cited in fringe literary circles for their lyrical solitude and emotional weight. These pieces showcase his mastery of melancholic prose and his ability to transform personal anguish into universal art."],t=[{title:"Frozen Eyes",year:1912,description:"Deeply tragic emotional novella exploring imaginary love and unfulfilled longing"},{title:"Quill in the Flame",year:1918,description:"Lyrical exploration of solitude and the burning passion of creation"},{title:"Nights of Roamers",year:1920,description:"Haunting collection examining restless souls and nocturnal wanderings"},{title:"Letters from the Edge",year:1922,description:"Prose poems and meditative essays on love, loss, and memory"},{title:"The Collected Fragments",year:1967,description:"Posthumously published complete works revealing his literary genius"}],n=[{text:"We are but shadows learning to cast light upon the darkness of love."},{text:"In frozen eyes, I see the winter of my own heart reflected."},{text:"The deepest truths are found not in what we know, but in what we dare to forget, and what we cannot help but remember."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Basil Daeren",quote:"In the silence between heartbeats, eternity whispers its secrets.",years:"1887-1924"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Ek(){const e=["James Augustine Aloysius Joyce (1882-1941) was an Irish novelist, poet, and literary critic, considered one of the most influential writers of the 20th century. Born in Dublin, Joyce spent most of his adult life in continental Europe, yet his fictional universe remained rooted in his native city.","Joyce revolutionized the novel with his stream-of-consciousness technique and experimental narrative methods. His masterpiece 'Ulysses' (1922) transformed modern literature, paralleling Homer's Odyssey while chronicling a single day in Dublin through the consciousness of its characters.","His final work, 'Finnegans Wake,' pushed linguistic experimentation to its limits, creating a dreamlike narrative that plays with multiple languages and meanings. Joyce's influence on modernist literature cannot be overstated, inspiring generations of writers to explore new forms of literary expression."],t=[{title:"Dubliners",year:1914,description:"Collection of short stories depicting middle-class life in Dublin"},{title:"A Portrait of the Artist as a Young Man",year:1916,description:"Semi-autobiographical bildungsroman"},{title:"Ulysses",year:1922,description:"Modernist masterpiece paralleling Homer's Odyssey"},{title:"Finnegans Wake",year:1939,description:"Experimental novel exploring dreams and language"},{title:"Chamber Music",year:1907,description:"Collection of lyric poems"}],n=[{text:"Mistakes are the portals of discovery."},{text:"I will not serve that in which I no longer believe whether it call itself my home, my fatherland or my church."},{text:"Every life is many days, day after day. We walk through ourselves, meeting robbers, ghosts, giants, old men, young men, wives, widows, brothers-in-love, but always meeting ourselves."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"James Joyce",quote:"History is a nightmare from which I am trying to awake.",years:"1882-1941"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Ck(){const e=["Jane Austen (1775-1817) was an English novelist known for her wit, social observation, and timeless exploration of love, marriage, and society in Regency England. Born into a close-knit family in rural Hampshire, she began writing at a young age and completed six major novels.","Austen's novels are celebrated for their sharp social commentary, memorable characters, and sparkling dialogue. She masterfully portrayed the constraints and expectations placed on women of her era while creating strong, intelligent heroines who navigate complex social situations with grace and determination.","Though she published her works anonymously during her lifetime, Austen's reputation has only grown over the centuries. Her novels continue to captivate readers worldwide and have been adapted countless times for film, television, and stage, cementing her place as one of literature's most beloved authors."],t=[{title:"Sense and Sensibility",year:1811,description:"Her first published novel contrasting reason and emotion"},{title:"Pride and Prejudice",year:1813,description:"Beloved romance exploring first impressions and true love"},{title:"Mansfield Park",year:1814,description:"Complex tale of morality and social climbing"},{title:"Emma",year:1815,description:"Comedy of manners featuring a well-meaning matchmaker"},{title:"Northanger Abbey",year:1817,description:"Gothic parody and coming-of-age story"},{title:"Persuasion",year:1817,description:"Mature exploration of second chances and true love"}],n=[{text:"It is a truth universally acknowledged, that a single man in possession of a good fortune, must be in want of a wife."},{text:"I declare after all there is no enjoyment like reading! How much sooner one tires of any thing than of a book!"},{text:"The little things are infinitely the most important."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Jane Austen",quote:"There is nothing like staying at home for real comfort.",years:"1775-1817"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Pk(){const e=["Ernest Miller Hemingway (1899-1961) was an American novelist, short-story writer, and journalist whose distinctive writing style profoundly influenced 20th-century fiction. Born in Illinois, he served as an ambulance driver in World War I, an experience that shaped his worldview and writing.","Hemingway developed a minimalist prose style characterized by understated emotion and sparse dialogue, famously described as the 'iceberg theory' - showing only the surface while the deeper meaning resonates beneath. His writing often explored themes of war, death, love, and the human condition.","A member of the 'Lost Generation' of expatriate writers in 1920s Paris, Hemingway lived adventurously, pursuing big-game hunting, deep-sea fishing, and war correspondence. He won the Pulitzer Prize and Nobel Prize in Literature, but struggled with depression and took his own life in 1961."],t=[{title:"The Sun Also Rises",year:1926,description:"Defining novel of the Lost Generation in post-WWI Europe"},{title:"A Farewell to Arms",year:1929,description:"Tragic love story set during World War I"},{title:"For Whom the Bell Tolls",year:1940,description:"Epic tale of love and sacrifice during the Spanish Civil War"},{title:"The Old Man and the Sea",year:1952,description:"Pulitzer Prize-winning novella about an aging Cuban fisherman"},{title:"Death in the Afternoon",year:1932,description:"Non-fiction work on the Spanish tradition of bullfighting"}],n=[{text:"All you have to do is write one true sentence. Write the truest sentence that you know."},{text:"The world breaks everyone, and afterward, some are strong at the broken places."},{text:"Courage is grace under pressure."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Ernest Hemingway",quote:"There is nothing to writing. All you do is sit down at a typewriter and bleed.",years:"1899-1961"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function jk(){const e=["James Arthur Baldwin (1924-1987) was an American novelist, essayist, playwright, poet, and social activist whose work explored the complexities of racial, sexual, and class distinctions in Western society. Born in Harlem, he became one of the most important voices of the Civil Rights Movement.","Baldwin's writing courageously addressed issues of race, sexuality, and identity with unprecedented honesty and eloquence. His essays, particularly those collected in 'Notes of a Native Son' and 'The Fire Next Time,' provided profound insights into the African American experience and challenged readers to confront uncomfortable truths about society.","Living much of his later life in France, Baldwin wrote both fiction and non-fiction that examined the psychological and social effects of racism. His work continues to resonate today, offering timeless wisdom about love, justice, and the ongoing struggle for human dignity and equality."],t=[{title:"Go Tell It on the Mountain",year:1953,description:"Semi-autobiographical novel about growing up in Harlem"},{title:"Notes of a Native Son",year:1955,description:"Groundbreaking collection of essays on race and identity"},{title:"Giovanni's Room",year:1956,description:"Pioneering novel exploring homosexuality and exile"},{title:"The Fire Next Time",year:1963,description:"Influential essays on race relations in America"},{title:"If Beale Street Could Talk",year:1974,description:"Love story set against systemic racial injustice"}],n=[{text:"Not everything that is faced can be changed, but nothing can be changed until it is faced."},{text:"Love takes off masks that we fear we cannot live without and know we cannot live within."},{text:"The most dangerous creation of any society is the man who has nothing to lose."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"James Baldwin",quote:"If you know whence you came, there is really no limit to where you can go.",years:"1924-1987"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Nk(){const e=["Toni Morrison (1931-2019), born Chloe Ardelia Wofford, was an American novelist, essayist, and editor who became the first African American woman to win the Nobel Prize in Literature in 1993. Her powerful novels explored the African American experience with unprecedented depth and artistry.","Morrison's writing is characterized by its lyrical prose, complex narrative structures, and unflinching examination of slavery's legacy and its impact on individual and collective memory. She gave voice to previously silenced stories, particularly those of African American women, with extraordinary compassion and literary skill.","Beyond her literary achievements, Morrison was a distinguished editor at Random House, where she championed works by African American authors. Her influence on American literature is immeasurable, as she opened new paths for understanding history, identity, and the enduring power of storytelling."],t=[{title:"The Bluest Eye",year:1970,description:"Debut novel exploring beauty standards and self-worth"},{title:"Sula",year:1973,description:"Complex portrait of friendship between two African American women"},{title:"Song of Solomon",year:1977,description:"Epic family saga blending realism with African American folklore"},{title:"Beloved",year:1987,description:"Pulitzer Prize-winning masterpiece about slavery's haunting legacy"},{title:"Jazz",year:1992,description:"Musical narrative set in 1920s Harlem"},{title:"Paradise",year:1997,description:"Ambitious novel about an all-black town in 1970s Oklahoma"}],n=[{text:"If you want to fly, you have to give up the things that weigh you down."},{text:"The function, the very serious function of racism is distraction."},{text:"Freeing yourself was one thing, claiming ownership of that freed self was another."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Toni Morrison",quote:"If there's a book that you want to read, but it hasn't been written yet, then you must write it.",years:"1931-2019"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Tk(){const e=["Gabriel José de la Concordia García Márquez (1927-2014) was a Colombian novelist, short-story writer, screenwriter, and journalist who became one of the most significant authors of the 20th century. He was awarded the Nobel Prize in Literature in 1982 for his novels and short stories.","García Márquez pioneered the literary style of magical realism, seamlessly blending fantastical elements with mundane reality to create a unique narrative voice that captured the essence of Latin American experience. His masterpiece 'One Hundred Years of Solitude' is considered one of the greatest novels ever written.","Born in Aracataca, Colombia, García Márquez drew inspiration from his grandmother's storytelling and the rich oral traditions of his homeland. His works often explore themes of solitude, love, power, and the cyclical nature of history, creating a literary universe that is both deeply local and universally resonant."],t=[{title:"One Hundred Years of Solitude",year:1967,description:"Magical realism masterpiece chronicling the Buendía family"},{title:"Love in the Time of Cholera",year:1985,description:"Epic love story spanning over fifty years"},{title:"Chronicle of a Death Foretold",year:1981,description:"Novella exploring honor, fate, and collective responsibility"},{title:"The Autumn of the Patriarch",year:1975,description:"Portrait of a Caribbean dictator's decline"},{title:"No One Writes to the Colonel",year:1961,description:"Poignant tale of hope and dignity in the face of poverty"}],n=[{text:"It is not true that people stop pursuing dreams because they grow old, they grow old because they stop pursuing dreams."},{text:"What matters in life is not what happens to you but what you remember and how you remember it."},{text:"The secret of a good old age is simply an honorable pact with solitude."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Gabriel García Márquez",quote:"It is not true that people stop pursuing dreams because they grow old, they grow old because they stop pursuing dreams.",years:"1927-2014"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Ak(){const e=["George Orwell (1903-1950), born Eric Arthur Blair, was an English novelist, essayist, journalist, and critic whose work is characterized by lucid prose, social criticism, and opposition to totalitarianism. His experiences as an imperial police officer in Burma and his observations of social inequality shaped his political consciousness.","Orwell's most famous works, '1984' and 'Animal Farm,' serve as powerful warnings against totalitarian regimes and the manipulation of truth. His concept of 'doublethink,' 'newspeak,' and 'Big Brother' has entered common usage, demonstrating the enduring relevance of his dystopian vision.","Beyond fiction, Orwell was a masterful essayist whose works like 'Politics and the English Language' continue to influence writers and thinkers. His commitment to democratic socialism and his belief in the importance of clear, honest language made him one of the most influential political writers of the 20th century."],t=[{title:"Animal Farm",year:1945,description:"Allegorical fable about the Russian Revolution and Stalinism"},{title:"1984",year:1949,description:"Dystopian masterpiece about totalitarian surveillance and control"},{title:"Down and Out in Paris and London",year:1933,description:"Memoir of poverty and social observation"},{title:"The Road to Wigan Pier",year:1937,description:"Investigation into working-class conditions in northern England"},{title:"Homage to Catalonia",year:1938,description:"Personal account of fighting in the Spanish Civil War"}],n=[{text:"War is peace. Freedom is slavery. Ignorance is strength."},{text:"In a time of deceit telling the truth is a revolutionary act."},{text:"All animals are equal, but some animals are more equal than others."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"George Orwell",quote:"If you want a picture of the future, imagine a boot stamping on a human face—forever.",years:"1903-1950"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Rk(){const e=["Federico García Lorca (1898-1936) was a Spanish poet, playwright, and theatre director who became one of the most important figures of the Generation of '27, a group of influential Spanish poets. Born in Andalusia, his work was deeply influenced by the folklore, music, and traditions of his native region.","Lorca's poetry and plays are characterized by their passionate intensity, surreal imagery, and exploration of themes such as death, love, and the clash between individual desires and social constraints. His work often featured strong female characters struggling against oppressive social norms.","His life was tragically cut short when he was executed by Nationalist forces at the beginning of the Spanish Civil War in 1936. Despite his brief career, Lorca's influence on Spanish literature and world poetry has been immense, with his works continuing to be performed and celebrated worldwide."],t=[{title:"Romancero Gitano",year:1928,description:"Gypsy Ballads - poetry collection celebrating Andalusian Roma culture"},{title:"Blood Wedding",year:1933,description:"Tragic play about passion, honor, and fate in rural Spain"},{title:"Yerma",year:1934,description:"Play about a woman's desperate desire for motherhood"},{title:"The House of Bernarda Alba",year:1936,description:"Final play about female oppression and desire"},{title:"Poet in New York",year:1940,description:"Surrealist poetry collection written during his time in New York"}],n=[{text:"Green, how I want you green. Green wind. Green branches."},{text:"The only things that the United States has given to the world are skyscrapers, jazz, and cocktails."},{text:"At five in the afternoon. It was exactly five in the afternoon."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Federico García Lorca",quote:"The weeping of the guitar begins.",years:"1898-1936"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Ok(){const e=["Yukio Mishima (1925-1970), born Kimitake Hiraoka, was a Japanese author, poet, playwright, actor, and political activist who became one of the most controversial and celebrated writers of post-war Japan. His work explored themes of beauty, death, and the conflict between traditional Japanese values and modern Western influence.","Mishima was fascinated by the samurai code of bushido and the aesthetic of death, themes that permeated much of his writing. His novels often featured protagonists torn between desire and duty, exploring the intersection of physical beauty, spiritual purity, and violent death.","His life ended dramatically in 1970 when he attempted a coup at a military base and, after its failure, committed ritual suicide by seppuku. This shocking end brought international attention to both his political views and his literary legacy, cementing his reputation as one of Japan's most complex and influential writers."],t=[{title:"Confessions of a Mask",year:1949,description:"Semi-autobiographical novel exploring homosexuality and identity"},{title:"The Temple of the Golden Pavilion",year:1956,description:"Novel about obsession, beauty, and destruction"},{title:"The Sea of Fertility",year:1965,description:"Tetralogy exploring reincarnation and Japanese history"},{title:"The Sailor Who Fell from Grace with the Sea",year:1963,description:"Dark tale of adolescence and violence"},{title:"Patriotism",year:1961,description:"Short story about love, honor, and ritual suicide"}],n=[{text:"The only people in this world I really trust are people who are not afraid to die."},{text:"True beauty is something that attacks, overpowers, robs, and finally destroys."},{text:"Human life is limited but I would like to live forever."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Yukio Mishima",quote:"The past does not only draw us back to the past. There are certain memories, and certain memories of love, that draw us forward to the future.",years:"1925-1970"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Lk(){const e=["Fernando António Nogueira Pessoa (1888-1935) was a Portuguese poet, writer, literary critic, translator, publisher, and philosopher who is considered one of the most significant literary figures of the 20th century and one of the greatest poets in the Portuguese language.","Pessoa is famous for his creation of heteronyms - fully developed fictional poets with their own biographies, philosophies, and writing styles. His most famous heteronyms include Alberto Caeiro, Ricardo Reis, and Álvaro de Campos, each representing different aspects of the human condition and literary expression.","Despite writing prolifically, most of Pessoa's work was unpublished during his lifetime, discovered in a trunk after his death. His innovative approach to identity, his philosophical depth, and his modernist techniques have influenced countless writers and established him as a cornerstone of Portuguese literature."],t=[{title:"Message",year:1934,description:"Epic poem celebrating Portuguese history and identity"},{title:"The Book of Disquiet",year:1982,description:"Philosophical fragments published posthumously"},{title:"Alberto Caeiro: The Keeper of Sheep",year:1925,description:"Poetry by Pessoa's most important heteronym"},{title:"Ricardo Reis: Odes",year:1946,description:"Classical poetry exploring stoicism and fate"},{title:"Álvaro de Campos: Poems",year:1944,description:"Futurist poetry celebrating modernity and sensation"}],n=[{text:"I am nothing. I shall always be nothing. I cannot want to be nothing. Apart from that, I have in me all the dreams of the world."},{text:"To be great, be entire; exclude nothing, exaggerate nothing that is not you."},{text:"Literature is the most agreeable way of ignoring life."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Fernando Pessoa",quote:"I have more souls than one.",years:"1888-1935"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Mk(){const e=["Jalal ad-Din Muhammad Rumi (1207-1273) was a 13th-century Persian poet, Islamic scholar, theologian, and Sufi mystic whose spiritual and philosophical teachings transcend cultural and religious boundaries. Born in present-day Afghanistan, his family fled the Mongol invasions when he was young.","Rumi's life was transformed by his encounter with the wandering dervish Shams of Tabriz, whose friendship sparked the mystical awakening that would inspire Rumi's greatest poetry. After Shams's mysterious disappearance, Rumi channeled his grief and spiritual longing into thousands of verses celebrating divine love.","His masterwork, the 'Masnavi,' is considered one of the greatest works of mystical poetry, often called 'the Quran in Persian.' Rumi's poetry explores themes of divine love, spiritual transformation, and the unity of all existence, making him one of the most widely read poets in the world today."],t=[{title:"Masnavi",year:1258,description:"Epic spiritual poem consisting of six books of mystical teachings"},{title:"Diwan-e Shams-e Tabrizi",year:1244,description:"Collection of lyrical poems inspired by his spiritual companion"},{title:"Fihi Ma Fihi",year:1250,description:"Prose work containing discourses and teachings"},{title:"Majalis-e Sab'a",year:1260,description:"Seven sermons on spiritual topics"},{title:"Maktubat",year:1265,description:"Collection of letters to disciples and friends"}],n=[{text:"Out beyond ideas of wrongdoing and rightdoing there is a field. I'll meet you there."},{text:"The wound is the place where the Light enters you."},{text:"Let yourself be silently drawn by the strange pull of what you really love. It will not lead you astray."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Jalal ad-Din Rumi",quote:"Yesterday I was clever, so I wanted to change the world. Today I am wise, so I am changing myself.",years:"1207-1273"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function _k(){const e=["Langston Hughes (1901-1967) was an American poet, social activist, novelist, playwright, and columnist who became one of the most important figures of the Harlem Renaissance. Born in Joplin, Missouri, he moved frequently during his childhood before settling in Harlem, New York.","Hughes was one of the first writers to celebrate African American culture and experience in his work, incorporating the rhythms of jazz and blues into his poetry. His writing addressed the struggles and joys of working-class African Americans with both dignity and artistic innovation.","Beyond poetry, Hughes wrote novels, short stories, and plays that explored themes of racial identity, social justice, and the American Dream. His commitment to representing the voice of common people and his belief in the power of art to create social change made him a defining voice of his generation."],t=[{title:"The Weary Blues",year:1926,description:"Debut poetry collection celebrating jazz culture and African American life"},{title:"Not Without Laughter",year:1930,description:"Novel about a young African American boy growing up in Kansas"},{title:"The Ways of White Folks",year:1934,description:"Short story collection examining racial tensions in America"},{title:"Montage of a Dream Deferred",year:1951,description:"Poetry collection exploring the deferred dreams of Harlem residents"},{title:"Simple Speaks His Mind",year:1950,description:"Collected stories featuring his beloved character Jesse B. Semple"}],n=[{text:"Hold fast to dreams, for if dreams die, life is a broken-winged bird that cannot fly."},{text:"What happens to a dream deferred? Does it dry up like a raisin in the sun?"},{text:"I, too, am America."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Langston Hughes",quote:"My soul has grown deep like the rivers.",years:"1901-1967"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Ik(){const e=["Margaret Eleanor Atwood (born 1939) is a Canadian poet, novelist, literary critic, essayist, teacher, environmental activist, and inventor. She is one of Canada's most celebrated authors and a leading voice in contemporary literature, known for her speculative fiction and feminist themes.","Atwood's work often explores themes of power, gender, identity, and environmental concerns through the lens of speculative fiction. Her dystopian novel 'The Handmaid's Tale' has become particularly relevant in contemporary political discourse, inspiring television adaptations and political movements worldwide.","With over fifty books of poetry, fiction, and non-fiction to her name, Atwood has received numerous awards including the Booker Prize and has been shortlisted multiple times. Her prescient vision of future societies and her masterful storytelling have made her one of the most important writers of our time."],t=[{title:"The Handmaid's Tale",year:1985,description:"Dystopian novel about reproductive oppression in a totalitarian state"},{title:"Cat's Eye",year:1988,description:"Coming-of-age novel exploring female friendship and artistic identity"},{title:"The Blind Assassin",year:2e3,description:"Booker Prize-winning novel within a novel"},{title:"Oryx and Crake",year:2003,description:"Post-apocalyptic novel beginning the MaddAddam trilogy"},{title:"The Testaments",year:2019,description:"Sequel to The Handmaid's Tale, winner of the Booker Prize"}],n=[{text:"A word after a word after a word is power."},{text:"Better never means better for everyone... It always means worse, for some."},{text:"We were the people who were not in the papers. We lived in the blank white spaces at the edges of print."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Margaret Atwood",quote:"Ignoring isn't the same as ignorance, you have to work at it.",years:"1939-"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function Dk(){const e=["Chinua Achebe (1930-2013) was a Nigerian novelist, poet, professor, and critic who is widely regarded as the most important African writer of his generation. Born Albert Chinụalụmọgụ Achebe in Ogidi, Nigeria, he played a crucial role in developing modern African literature.","Achebe's groundbreaking novel 'Things Fall Apart' became one of the most widely read works of African literature, offering a complex portrayal of pre-colonial Igbo society and the impact of European colonialism. His work challenged Western stereotypes about Africa and gave voice to African perspectives on their own history and culture.","Beyond his literary achievements, Achebe was a passionate advocate for African identity and cultural dignity. As a teacher, critic, and cultural commentator, he inspired generations of African writers and helped establish African literature as a vital force in world literature."],t=[{title:"Things Fall Apart",year:1958,description:"Landmark novel about colonialism's impact on Igbo society"},{title:"No Longer at Ease",year:1960,description:"Sequel exploring post-colonial Nigerian society"},{title:"Arrow of God",year:1964,description:"Novel about conflict between traditional beliefs and colonial rule"},{title:"A Man of the People",year:1966,description:"Political satire about corruption in post-independence Nigeria"},{title:"The Education of a British-Protected Child",year:2009,description:"Collection of essays on literature, politics, and culture"}],n=[{text:"Until the lions have their own historians, the history of the hunt will always glorify the hunter."},{text:"The white man is very clever. He came quietly and peaceably with his religion. We were amused at his foolishness and allowed him to stay."},{text:"A man who calls his kinsmen to a feast does not do so to save them from starving. They all have food in their own homes."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Chinua Achebe",quote:"If you don't like someone's story, write your own.",years:"1930-2013"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}function zk(){const e=["Walt Whitman (1819-1892) was an American poet, essayist, and journalist who is often called the father of free verse poetry. Born on Long Island, New York, he spent much of his life celebrating the democratic spirit and diversity of America through his revolutionary poetic style.","Whitman's masterpiece 'Leaves of Grass' broke traditional poetic conventions with its free verse, expansive catalogs, and celebration of the human body and spirit. His poetry embraced all aspects of American life, from the industrial to the pastoral, the sacred to the secular, creating a uniquely American voice in literature.","During the Civil War, Whitman served as a volunteer nurse in Washington hospitals, experiences that deepened his empathy and influenced his later poetry. His vision of America as a democratic experiment and his belief in the dignity of all people made him one of the most influential voices in American literature."],t=[{title:"Leaves of Grass",year:1855,description:"Revolutionary poetry collection celebrating American democracy and individuality"},{title:"Song of Myself",year:1855,description:"Epic poem of self-celebration and democratic ideals"},{title:"Drum-Taps",year:1865,description:"Civil War poetry collection reflecting on conflict and healing"},{title:"Democratic Vistas",year:1871,description:"Prose work on American democracy and culture"},{title:"Specimen Days",year:1882,description:"Autobiographical prose reflecting on life and times"}],n=[{text:"Do I contradict myself? Very well then I contradict myself, (I am large, I contain multitudes.)"},{text:"Keep your face always toward the sunshine—and shadows will fall behind you."},{text:"I celebrate myself, and sing myself, And what I assume you shall assume, For every atom belonging to me as good belongs to you."}];return l.jsxs("div",{className:"min-h-screen bg-cream",children:[l.jsx(J,{}),l.jsx(ee,{name:"Walt Whitman",quote:"The United States themselves are essentially the greatest poem.",years:"1819-1892"}),l.jsx(te,{title:"Biography",content:e}),l.jsx(ne,{works:t}),l.jsx(re,{quotes:n})]})}const Fk=new Y1,Bk=()=>l.jsx(X1,{client:Fk,children:l.jsxs(j1,{children:[l.jsx(Cw,{}),l.jsx(e0,{}),l.jsx(Kb,{children:l.jsxs($b,{children:[l.jsx(K,{path:"/",element:l.jsx(Xb,{})}),l.jsx(K,{path:"/authors",element:l.jsx(tk,{})}),l.jsx(K,{path:"/quotes",element:l.jsx(rk,{})}),l.jsx(K,{path:"/about",element:l.jsx(ok,{})}),l.jsx(K,{path:"/search",element:l.jsx(sk,{})}),l.jsx(K,{path:"/author/dostoevsky",element:l.jsx(uk,{})}),l.jsx(K,{path:"/author/kafka",element:l.jsx(ck,{})}),l.jsx(K,{path:"/author/chekhov",element:l.jsx(dk,{})}),l.jsx(K,{path:"/author/wilde",element:l.jsx(fk,{})}),l.jsx(K,{path:"/author/plath",element:l.jsx(hk,{})}),l.jsx(K,{path:"/author/shakespeare",element:l.jsx(pk,{})}),l.jsx(K,{path:"/author/dickinson",element:l.jsx(mk,{})}),l.jsx(K,{path:"/author/poe",element:l.jsx(gk,{})}),l.jsx(K,{path:"/author/woolf",element:l.jsx(yk,{})}),l.jsx(K,{path:"/author/rilke",element:l.jsx(vk,{})}),l.jsx(K,{path:"/author/tolstoy",element:l.jsx(xk,{})}),l.jsx(K,{path:"/author/borges",element:l.jsx(wk,{})}),l.jsx(K,{path:"/author/camus",element:l.jsx(bk,{})}),l.jsx(K,{path:"/author/neruda",element:l.jsx(kk,{})}),l.jsx(K,{path:"/author/basil-daeren",element:l.jsx(Sk,{})}),l.jsx(K,{path:"/author/joyce",element:l.jsx(Ek,{})}),l.jsx(K,{path:"/author/austen",element:l.jsx(Ck,{})}),l.jsx(K,{path:"/author/hemingway",element:l.jsx(Pk,{})}),l.jsx(K,{path:"/author/baldwin",element:l.jsx(jk,{})}),l.jsx(K,{path:"/author/morrison",element:l.jsx(Nk,{})}),l.jsx(K,{path:"/author/garcia-marquez",element:l.jsx(Tk,{})}),l.jsx(K,{path:"/author/orwell",element:l.jsx(Ak,{})}),l.jsx(K,{path:"/author/lorca",element:l.jsx(Rk,{})}),l.jsx(K,{path:"/author/mishima",element:l.jsx(Ok,{})}),l.jsx(K,{path:"/author/pessoa",element:l.jsx(Lk,{})}),l.jsx(K,{path:"/author/rumi",element:l.jsx(Mk,{})}),l.jsx(K,{path:"/author/hughes",element:l.jsx(_k,{})}),l.jsx(K,{path:"/author/atwood",element:l.jsx(Ik,{})}),l.jsx(K,{path:"/author/achebe",element:l.jsx(Dk,{})}),l.jsx(K,{path:"/author/whitman",element:l.jsx(zk,{})}),l.jsx(K,{path:"*",element:l.jsx(Zb,{})})]})})]})});Tp(document.getElementById("root")).render(l.jsx(Bk,{}));
